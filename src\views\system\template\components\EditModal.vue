<template>
  <el-dialog v-model="visible" width="50%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="20">
          <el-col :span="12">
            <el-form-item :label="$t('供应商')" prop="supplier">
              <el-select v-model="form.supplier" :placeholder="t('请选择')" clearable>
                <el-option v-for="item in supplierList" :key="item.id" :label="item.supplier" :value="item.supplier" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('制程名称')" prop="process">
              <el-select v-model="form.process">
                <el-option v-for="(item, index) in process_name" :key="index" :value="item.label">{{ item.label }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('制程类型')" prop="processType">
              <el-select v-model="form.processType">
                <el-option v-for="(item, index) in process_type" :key="index" :value="item.label">{{ item.label }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('模板')" prop="postCode">
              <UploadFiles
                :file-url="fileUrls"
                :multiple="false"
                :is-show-tip="true"
                :immediate="true"
                :limit="1"
                :file-type="['pdf', 'xls', 'xlsx']"
                :btn-name="$t('上传附件')"
                @upload-success="refFileSuccess"
                @file-list-empty="refFileEmpty"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('备注')">
              <el-input
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="form.remark"
                :placeholder="$t('请填写')"
                clearable
              />
            </el-form-item>
          </el-col>
          <!--          <el-col>-->
          <!--            <el-form-item :label="$t('状态')" prop="status">-->
          <!--              <el-radio-group v-model="form.status">-->
          <!--                <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{-->
          <!--                  $t(label)-->
          <!--                }}</el-radio>-->
          <!--              </el-radio-group>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="EditModal">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed, markRaw } from "vue";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { useDict } from "@/hooks/useDict";
import { Template } from "@/typings/template";
import UploadFiles from "@/components/Upload/UploadFiles.vue";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
const { t } = useI18n();

const { process_type, process_name } = useDict("process_type", "process_name");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Template.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  supplier: [{ required: true, message: t("请选择"), trigger: "blur" }],
  process: [{ required: true, message: t("请选择"), trigger: "blur" }],
  processType: [{ required: true, message: t("请选择"), trigger: "blur" }],
  attachmentUrl: [{ required: true, message: t("请上传"), trigger: "blur" }]
};
const fileUrls = ref<any[]>([]);
const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));
const supplierList = ref<Supplier.Item[]>([]);

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项
const refFileSuccess = (res: { name: string; url: string }) => {
  form.value.attachmentName = res.name;
  form.value.attachmentUrl = res.url;
};
const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};
const refFileEmpty = () => {
  form.value.attachmentName = "";
  form.value.attachmentUrl = "";
};
const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  await getSupplierData();
  fileUrls.value = form.value.attachmentName ? [{ name: form.value.attachmentName, url: form.value.attachmentUrl }] : [];
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value, postSort: 1 });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
