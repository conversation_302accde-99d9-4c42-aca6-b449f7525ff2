<template>
  <div class="w-full upload-file">
    <el-upload
      action="#"
      ref="fileUpload"
      class="upload-file-uploader"
      :multiple="multiple"
      :limit="limit"
      :on-error="uploadError"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :http-request="handleHttpUpload"
      :show-file-list="false"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">{{ btnName }}</el-button>
    </el-upload>
    <slot name="upload-tip">
      <div class="el-upload__tip" v-if="showTip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </slot>
    <!-- 上传提示 -->

    <!-- 文件列表 -->
    <transition-group
      v-if="showFileList"
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <FileItem v-for="fileId of _fileList" :key="fileId" :id="fileId" :base-url="baseUrl!" @handle-remove="handleRemove" />
    </transition-group>
  </div>
</template>

<script lang="tsx" setup name="FileUpload">
import { ref, computed, watch } from "vue";
import { ElNotification } from "element-plus";
import { uploadImg } from "@/api/modules/upload";
import { isEmpty } from "@/utils/is";
import FileItem from "./FileItem.vue";
import type { UploadProps, UploadRequestOptions } from "element-plus";

interface UploadFileProps {
  fileIds: string;
  immediate: boolean;
  baseUrl?: string;
  isAll?: boolean;
  btnName?: string;
  isShowTip?: boolean;
  showFileList?: boolean;
  api?: (params: any) => Promise<any>; // 上传图片的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
  drag?: boolean; // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
  limit?: number; // 最大图片上传数 ==> 非必传（默认为 5张）
  fileSize?: number; // 图片大小限制 ==> 非必传（默认为 5M）
  fileType?: any[]; // 图片类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/gif"]）
  multiple?: boolean;
}
const props = withDefaults(defineProps<UploadFileProps>(), {
  fileIds: "",
  drag: true,
  immediate: false,
  isAll: false,
  btnName: "选取文件",
  baseUrl: import.meta.env.VITE_PREVIEW_BASE_URL,
  disabled: false,
  isShowTip: true,
  showFileList: true,
  limit: 5,
  fileSize: 20,
  multiple: true,
  fileType: () => ["doc", "docx", "xls", "ppt", "pptx", "txt", "pdf"]
});

interface IEmits {
  (e: "update:fileIds", fileIds: string): void;
}

const emit = defineEmits<IEmits>();

const _fileList = ref<number[]>([]);

const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

watch(
  () => props.fileIds,
  (n: string) => {
    if (isEmpty(n)) return;
    _fileList.value = n.split(";").map(Number);
  },
  { immediate: props.immediate }
);

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  if (props.isAll) {
    return true;
  }
  const fileSize = rawFile.size / 1024 / 1024 < props.fileSize;
  const extName = rawFile.name.split(".").pop();
  const fileType = props.fileType.includes(extName);
  if (!fileType)
    ElNotification({
      title: "温馨提示",
      message: "上传文件不符合所需的格式！",
      type: "warning"
    });
  if (!fileSize)
    setTimeout(() => {
      ElNotification({
        title: "温馨提示",
        message: `上传文件大小不能超过 ${props.fileSize}M！`,
        type: "warning"
      });
    }, 0);
  return fileType && fileSize;
};

const handleHttpUpload = async (options: UploadRequestOptions) => {
  let formData = new FormData();
  formData.append("file", options.file, encodeURIComponent(options.file.name));
  try {
    const api = props.api ?? uploadImg;
    const { data } = await api(formData);
    if (props.multiple) {
      _fileList.value.push(data.id);
    } else {
      _fileList.value = [data.id];
    }
    emit("update:fileIds", _fileList.value.join(";"));
    ElNotification({
      title: "温馨提示",
      message: "文件上传成功！",
      type: "success"
    });
  } catch (error) {
    options.onError(error as any);
  }
};

const handleExceed = () => {
  ElNotification({
    title: "温馨提示",
    message: `当前最多只能上传 ${props.limit} 个文件，请移除后上传！`,
    type: "warning"
  });
};

/**
 * @description 图片上传错误
 * */
const uploadError = () => {
  ElNotification({
    title: "温馨提示",
    message: "文件上传失败，请您重新上传！",
    type: "error"
  });
};

/**
 * @description 删除图片
 * @param file 删除的文件
 * */
const handleRemove = (id: number) => {
  _fileList.value = _fileList.value.filter(item => item !== id);
  emit("update:fileIds", _fileList.value.join(";"));
};
</script>

<style scoped lang="scss">
.upload-file-list .el-upload-list__item {
  position: relative;
  margin-bottom: 10px;
  line-height: 2;
  border: 1px solid #e4e7ed;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-left: 10px;
}
</style>
