<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}`)">
    <template #header="{ titleId, titleClass }">
      <div class="my-header">
        <span :id="titleId" :class="titleClass">{{ $t(title) }}</span>
        <el-tag style="margin-left: 20px" type="primary">{{ $t(form.reportStatus) }}</el-tag>
      </div>
    </template>
    <el-form
      ref="formRef"
      label-width="110px"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      label-position="top"
    >
      <el-descriptions class="margin-top" :title="$t('异常信息')" :column="3" size="large" border>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">{{ $t("异常编号") }}</div>
          </template>
          {{ form.reportNo }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">{{ $t("异常类型") }}</div>
          </template>
          {{ form.exceptionType }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">{{ $t("供应商") }}</div>
          </template>
          {{ form.supplierName }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">{{ $t("产品名称") }}</div>
          </template>
          {{ form.product }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">{{ $t("NCMR") }}</div>
          </template>
          {{ form.sourceNo }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">{{ $t("当前节点") }}</div>
          </template>
          {{ form.currentNodeName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">
              {{ $t("异常标题") }}
            </div>
          </template>
          {{ form.title }}
        </el-descriptions-item>
      </el-descriptions>
      <p class="item-title">{{ $t("节点信息") }}</p>
      <!--      <div style="height: 2000px">-->
      <el-tabs stretch tab-position="left" v-model="nodeValue" @tab-change="getCurrentNodeInfo">
        <el-tab-pane
          v-for="(item, index) in nodesOptions"
          :key="index"
          :disabled="item.disabled"
          :name="index"
          :label="item.nodeTypeName"
          lazy
        >
          <template #label>
            <span class="custom-tabs-label">
              {{ item.disabled }}
              <!--              <el-icon><Document /></el-icon>-->
              <!--              <span :class="{ redPoint: initNodesOptions[index].rejected == 1 }"> {{ item.nodeTypeName }}</span>-->
              <el-badge :is-dot="initNodesOptions[index].rejected == 1" class="item">{{ item.nodeTypeName }}</el-badge>
            </span>
          </template>
          <el-form-item
            :label="$t('是否退回')"
            v-if="handleType === 'review' || (handleType === 'view' && nodesOptions[index].rejected == 1)"
          >
            <el-radio-group v-model="nodesOptions[index].rejected" :disabled="handleType !== 'review'">
              <el-radio :value="m.value" v-for="m in review_yse_or_no" :key="m.value">{{ m.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="
              (handleType === 'review' && nodesOptions[index].rejected == 1) ||
              (handleType === 'view' && nodesOptions[index].rejected == 1)
            "
            :label="$t('审批修改意见')"
          >
            <el-input
              :disabled="!(handleType === 'review')"
              :autosize="{ minRows: 4, maxRows: 8 }"
              type="textarea"
              v-model="nodesOptions[index].rejectionReason"
              :placeholder="$t('请填写修改意见')"
              clearable
            />
          </el-form-item>
          <!--          <el-form-item label="填报修改意见" v-if="handleType === 'view' || handleType === 'input'">-->
          <!--            <el-input-->
          <!--              :disabled="!(handleType === 'input')"-->
          <!--              :autosize="{ minRows: 4, maxRows: 8 }"-->
          <!--              type="textarea"-->
          <!--              v-model="nodesOptions[index].revisionComments"-->
          <!--              :placeholder="$t('请填写修改意见')"-->
          <!--              clearable-->
          <!--            />-->
          <!--          </el-form-item>-->
          <div class="tab-head">
            <span class="left">{{ nodesOptions[nodeValue]?.nodeTypeName }}</span>
            <span class="right">{{ nodesOptions[nodeValue]?.nodeTypeName }} Complete: {{ nodesOptions[index]?.createTime }}</span>
          </div>
          <wangEditor
            :disabled="handleType !== 'input'"
            height="200px"
            :toolbar-config="{
              toolbarKeys: [
                'bold',
                'underline',
                'italic',
                'foreColor',
                'backColor',
                'quote',
                'code',
                'splitLine',
                'list',
                'justify',
                'fontSize',
                'fontName',
                'link',
                'unlink'
              ]
            }"
            v-model:value="nodesOptions[index].content"
          />
          <el-form-item :label="$t('上传附件')" style="margin-top: 30px">
            <UploadFiles
              v-if="(fileUrls.length > 0 && handleType === 'review') || handleType === 'input'"
              :show-delete="handleType === 'input'"
              :disabled="handleType !== 'input'"
              :file-url="fileUrls"
              :multiple="false"
              :is-show-tip="true"
              :immediate="true"
              :limit="1"
              :file-type="['pdf', 'xls', 'xlsx']"
              :btn-name="$t('上传附件')"
              @upload-success="refFileSuccess"
              @file-list-empty="refFileEmpty"
            />
            <span v-else>{{ $t("未上传任何附件") }}</span>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
      <!--      </div>-->
    </el-form>
    <template #footer>
      <el-button v-if="handleType === 'view'" @click="setVisible(false)">{{ $t("关闭") }}</el-button>
      <el-button v-if="handleType === 'input' && showInputButtons" type="primary" @click="handleSubmit('save')">{{
        $t("保存")
      }}</el-button>
      <el-button v-if="handleType === 'input' && showInputButtons" type="primary" @click="handleSubmit('submit')">{{
        $t("提交")
      }}</el-button>
      <el-button v-if="handleType === 'review'" type="primary" @click="handleSubmit('pass')">{{ $t("批准") }}</el-button>
      <el-button v-if="handleType === 'review'" type="primary" @click="handleSubmit('back')">{{ $t("退回") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="RoleModal">
import { ExceptionManageForm } from "@/typings/exception-manage";
import { ElMessage, FormInstance, ElTreeSelect } from "element-plus";
import UploadFiles from "@/components/Upload/UploadFiles.vue";
import wangEditor from "@/components/wangEditor/index.vue";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useDict } from "@/hooks/useDict";
import { exceptionReportsNodeList, exceptionReportsNodeSave, exceptionReportsNodeSubmit } from "@/api/modules/exceptionReports";
import { exceptionManageInfo } from "@/api/modules/exception-manage";
import { ExceptionReportsNodeItem } from "@/typings/exceptionReports";
import { exceptionReportsApprove, exceptionReportsReject } from "@/api/modules/reportReview";
const { review_yse_or_no } = useDict("review_yse_or_no");

interface IState {
  title: string;
  isView: boolean;
  form: Partial<ExceptionManageForm>;
  // common_status: Dict.IDataItem[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  fileUrls: any[];
  responsibleList: any[];
  handleType: string;
}

const nodesOptions = ref<ExceptionReportsNodeItem[]>([]);
const initNodesOptions = ref<ExceptionReportsNodeItem[]>([]);
const nodeValue = ref();
const currentType = ref(0);

const { t } = useI18n();

const rules = {
  exceptionType: [{ required: true, message: t("请选择"), trigger: "blur" }],
  supplierName: [{ required: true, message: "请填写", trigger: "blur" }],
  product: [{ required: true, message: "请选择", trigger: "blur" }],
  responsibleBy: [{ required: true, message: "请选择", trigger: "blur" }]
};

// const treeSelectRef = ref<InstanceType<typeof ElTreeSelect> | null>();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  // common_status: [],
  form: {},
  fileUrls: [],
  responsibleList: [],
  handleType: ""
});

// const menuList = ref<Menu.Item[]>([]);

const { form, title, isView, fileUrls, handleType } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

const refFileSuccess = (res: { name: string; url: string }) => {
  nodesOptions.value[nodeValue.value].attachmentName = res.name;
  nodesOptions.value[nodeValue.value].attachmentUrl = res.url;
};

const refFileEmpty = () => {
  nodesOptions.value[nodeValue.value].attachmentName = "";
  nodesOptions.value[nodeValue.value].attachmentUrl = "";
};

const getExceptionReportsNodeList = async () => {
  const { data } = await exceptionReportsNodeList({ id: form.value.id });
  let disabled = false;
  nodesOptions.value = data.map(item => {
    // if (handleType.value === "input") {
    //   if (item.filling === 1) {
    //     item.disabled = true;
    //   }
    //
    //   if (item.filling === 0) {
    //     item.disabled = false;
    //     disabled = true;
    //   }
    //   //
    //   if (disabled) {
    //     item.disabled = true;
    //   }
    // }
    item.rejected = String(item.rejected);
    return item;
  });

  console.log(nodesOptions.value);

  initNodesOptions.value = JSON.parse(JSON.stringify(nodesOptions.value));
  //   .map(item => {
  //   item.label = item.nodeTypeName;
  //   item.value = item.id;
  //   return item;
  // });
  nodeValue.value = 0;
  getCurrentNodeInfo();
};

const getCurrentNodeInfo = () => {
  const obj: ExceptionReportsNodeItem | any = nodesOptions.value[nodeValue.value];
  if (obj) {
    const _obj = JSON.parse(JSON.stringify(obj));
    fileUrls.value = _obj.attachmentName ? [{ name: _obj.attachmentName, url: _obj.attachmentUrl }] : [];
    currentType.value = _obj.nodeType;
  } else {
    fileUrls.value = [];
  }
};
const getExceptionManageInfo = async () => {
  const { data } = await exceptionManageInfo({ id: form.value.id });
  Object.assign(state, { form: { ...form.value, ...data } });
};

const showInputButtons = computed(() => {
  if (nodeValue.value === 0) {
    return true;
  } else {
    if (nodesOptions.value[nodeValue.value - 1].filling !== 1) {
      return false;
    } else {
      return true;
    }
  }
});
//获取单条异常报告详情
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  await getExceptionManageInfo();
  await getExceptionReportsNodeList();
  setVisible(true);
};
// 提交数据（新增/编辑）
const handleSubmit = type => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      let fun: Function;
      let params = undefined;
      const nodeObj: ExceptionReportsNodeItem | any = nodesOptions.value[nodeValue.value];

      if (type === "save") {
        params = {
          ...nodeObj
        };
        fun = exceptionReportsNodeSave;
      }
      if (type === "submit") {
        params = {
          ...nodeObj
        };
        if ([3, 4, 8].includes(nodeObj.nodeType)) {
          return;
        }
        fun = exceptionReportsNodeSubmit;
      }
      if (type === "pass" || type === "back") {
        params = nodesOptions.value.map(item => {
          const { id, reportId, nodeType, nodeTypeName, rejected, rejectionReason, remark } = item;
          return item;
        });
      }
      if (type === "pass") {
        fun = exceptionReportsApprove;
      }

      if (type === "back") {
        fun = exceptionReportsReject;
      }

      await fun!(params);
      ElMessage.success({ message: t(`操作成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style>
.tree-border {
  width: 100%;
  margin-top: 5px;
  background: #ffffff none;
  border: 1px solid #e5e6e7;
  border-radius: 4px;
}
.item-title {
  font-weight: bold;
  font-size: 18px;
  line-height: 30px;
  margin: 30px 0 20px 0;
}

.tab-head {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  .right {
    float: right;
  }
}
.redPoint {
  position: relative;
  display: inline-block;
  &:before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 5px;
    border-radius: 5px;
    background: red;
  }
}
</style>
