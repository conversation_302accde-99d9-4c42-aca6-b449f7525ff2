<template>
  <el-dialog v-model="visible" :destroy-on-close="true" :title="$t(`${title}人员`)">
    <el-form
      ref="formRef"
      label-suffix=" :"
      :label-width="labelWidth"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :show-message="isZh"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('姓名')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('请填写用户姓名')" :disabled="disabled" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('工号')" prop="jobNum">
            <el-input v-model="form.jobNum" :placeholder="$t('请填写工号')" :disabled="disabled" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('职位')" prop="post">
            <el-select v-model="form.postId" :placeholder="$t('请选择职位')">
              <el-option v-for="{ name, id } of postList" :key="id" :label="$t(name)" :value="id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('职级')" prop="rank">
            <el-select v-model="form.rank" :placeholder="$t('请选择职级')">
              <el-option v-for="({ label, value }, index) of job_grade" :key="index" :label="$t(label)" :value="value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('直属领导')" prop="leader">
            <SelectScroll
              v-model="leader"
              :field-options="{ label: 'name', value: 'id' }"
              :list="leaderOptions"
              :load="staffLoad"
              :filter-method="filterLeaderMethod"
              :placeholder="$t('请选择直属领导')"
              class-name="leader-item"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('间接领导')" prop="indirectLeader">
            <SelectScroll
              v-model="indirectLeader"
              :field-options="{ label: 'name', value: 'id' }"
              :list="indirectLeaderOptions"
              :load="staffLoad"
              :immediate="false"
              :filter-method="filterIndirectLeaderMethod"
              :placeholder="$t('请选择间接领导')"
              class-name="indirect-leader-item"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('手机号码')" prop="tel">
            <el-input v-model="form.tel" :placeholder="$t('请填写手机号码')" :disabled="disabled" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('邮箱')" prop="email">
            <el-input v-model="form.email" :placeholder="$t('请填写邮箱')" :disabled="disabled" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('部门')" prop="dept">
            <el-tree-select
              v-model="form.deptId"
              :data="treeDeptList"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              :placeholder="$t('请选择部门')"
              value-key="id"
              check-strictly
            />
          </el-form-item>
        </el-col>
        <el-col v-if="!isEmpty(currentDept?.parentId)" :span="12">
          <el-form-item :label="$t('上级部门')">
            <el-input :model-value="parentDept?.name" :placeholder="$t('请填写上级部门')" :disabled="true" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('公司/工厂')">
            <el-input :model-value="rootDept?.name" :placeholder="$t('请填写上级部门')" :disabled="true" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('角色')" prop="roleId">
            <el-select v-model="form.roleId" :placeholder="$t('请选择角色')" multiple>
              <el-option v-for="{ name, id } of roleList" :key="id" :label="name" :value="id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('状态')">
            <el-radio-group :disabled="disabled" v-model="form.status">
              <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{ $t(label) }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="StaffModal">
import { ref, reactive, computed, toRefs, watch } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { User } from "@/api/interface";
import { Staff } from "@/typings/staff";
import { Post } from "@/typings/post";
import { Dict } from "@/typings/dict";
import { Dept } from "@/typings/dept";
import { Role } from "@/typings/role";
import { isEmpty } from "@/utils/is";
import { findTopParent } from "@/utils";
import { getStaffList } from "@/api/modules/staff";
import { useSelectScroll } from "@/hooks/useSelectScroll";
import SelectScroll from "@/components/SelectScroll/index.vue";
import { useLanguageCode } from "@/hooks/useLanguageCode";

const rules = reactive<FormRules>({
  name: [{ required: true, message: "请填写员工姓名" }],
  jobNum: [{ required: true, message: "请填写员工工号" }],
  tel: [{ message: "请填写正确的手机号", pattern: /^1\d{10}$/ }],
  // email: [
  //   { required: true, message: "请填写邮箱", trigger: "blur" },
  //   {
  //     type: "email",
  //     message: "请填写正确邮箱",
  //     trigger: ["blur", "change"]
  //   }
  // ],
  roleId: [{ required: true, message: "请选择角色" }]
});

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<User.ResUserList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

interface IState {
  title: string;
  isView: boolean;
  form: Partial<Staff.Item>;
  postList: Post.Item[];
  treeDeptList: Dept.Item[];
  flatDeptList: Dept.Item[];
  staffList: Staff.Item[];
  roleList: Role.Item[];
  common_status: Dict.IDataItem[];
  job_grade: Dict.IDataItem[];
  leader: string | number;
  indirectLeader: string | number;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "140px" : "180px"));

const formRef = ref<FormInstance>();
const state = reactive<IState>({
  isView: false,
  title: "",
  common_status: [],
  staffList: [],
  job_grade: [],
  postList: [],
  form: {},
  treeDeptList: [],
  flatDeptList: [],
  roleList: [],
  leader: "",
  indirectLeader: ""
});

const leaderKeyword = ref<string>("");
const indirectLeaderKeyword = ref<string>("");

const { options: staffOpt, load: staffLoad } = useSelectScroll({}, getStaffList);

const {
  form,
  isView,
  title,
  postList,
  common_status,
  job_grade,
  treeDeptList,
  flatDeptList,
  roleList,
  leader,
  indirectLeader,
  staffList
} = toRefs(state);
const visible = ref<boolean>(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

const currentDept = computed(() => {
  if (!isEmpty(form.value.deptId)) {
    return flatDeptList.value.find(dept => dept.id === form.value.deptId);
  }
  return {} as Dept.Item;
});

const indirectLeaderInfo = computed(() => {
  return staffOpt.data.find(item => item.id === form.value.leader) ?? indirectLeader.value;
});

const parentDept = computed(() => {
  if (!isEmpty(currentDept.value?.parentId)) {
    return flatDeptList.value.find(dept => dept.id === currentDept.value!.parentId);
  }
  return {} as Dept.Item;
});

const rootDept = computed(() => {
  if (!isEmpty(form.value.deptId)) {
    return findTopParent(flatDeptList.value, form.value.deptId!);
  }
  return {} as Dept.Item;
});

const disabled = computed(() => {
  return drawerProps.value.title === "编辑";
});

const leaderOptions = computed(() => {
  if (isEmpty(leaderKeyword.value)) return staffOpt.data;
  return staffList.value.filter(v => v.name.includes(leaderKeyword.value));
});
const indirectLeaderOptions = computed(() => {
  if (isEmpty(indirectLeaderKeyword.value)) return staffOpt.data;
  return staffList.value.filter(v => v.name.includes(indirectLeaderKeyword.value));
});

const filterLeaderMethod = (keyword: string) => {
  leaderKeyword.value = keyword;
};
const filterIndirectLeaderMethod = (keyword: string) => {
  indirectLeaderKeyword.value = keyword;
};
const setVisible = (val: boolean) => {
  visible.value = val;
};

// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    if (!isEmpty(leader.value) && !isNaN(Number(leader.value))) {
      form.value.leader = Number(leader.value);
    }
    if (!isEmpty(indirectLeader.value) && !isNaN(+indirectLeader.value)) {
      form.value.indirectLeader = +indirectLeader.value;
    }
    try {
      await state.api!({
        ...form.value,
        deptParent: parentDept.value?.id,
        company: rootDept.value?.id
      });
      ElMessage.success({ message: `${title.value}成功！` });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

watch(leader, val => {
  if (!isEmpty(val) && !isNaN(Number(val))) {
    const indirectLeaderId = staffOpt.data.find(item => item.id === Number(val))?.leader ?? "";
    indirectLeader.value = indirectLeaderId;
  }
});

defineExpose({
  acceptParams
});
</script>
