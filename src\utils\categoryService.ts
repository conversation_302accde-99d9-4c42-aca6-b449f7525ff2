import { ref } from "vue";
import { getSmtCategoryList } from "@/api/modules/ipqc-smt/ipqc_smt_category";
import { SmtCategory } from "@/typings/ipqc-smt/ipqc_smt_category";

export const categoryList = ref<SmtCategory.Item[]>([]);
export const getCategoryList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getSmtCategoryList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      categoryList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
