<template>
  <el-form ref="loginFormRef" :model="registerForm" :rules="loginRules" size="large">
    <!--    <el-form-item prop="jobNum">-->
    <!--      <el-input v-model="registerForm.jobNum" :placeholder="$t('请输入员工工号')" />-->
    <!--    </el-form-item>-->
    <!--    <el-form-item prop="name">-->
    <!--      <el-input v-model="registerForm.name" :placeholder="$t('请输入姓名')" />-->
    <!--    </el-form-item>-->
    <el-form-item prop="username">
      <el-input v-model="registerForm.username" :placeholder="$t('请输入账号')" />
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        v-model="registerForm.password"
        type="password"
        show-password
        autocomplete="new-password"
        placeholder="请输入密码"
      />
    </el-form-item>
    <el-form-item prop="confirmPassword">
      <el-input
        v-model="registerForm.confirmPassword"
        type="password"
        show-password
        autocomplete="new-password"
        placeholder="请输入确认密码"
      />
    </el-form-item>
    <!--    <el-form-item prop="roleId">-->
    <!--      <el-select v-model="registerForm.roleId" :placeholder="$t('请选角色')">-->
    <!--        <el-option v-for="{ name, id } of roleList" :key="id" :label="name" :value="id" />-->
    <!--      </el-select>-->
    <!--    </el-form-item>-->
    <!-- <el-form-item prop="remark">
      <el-input v-model="registerForm.remark" placeholder="请输入备注" />
    </el-form-item> -->
  </el-form>

  <div class="login-btn">
    <el-button size="large" type="primary" :loading="loading" @click="login(loginFormRef)"> {{ $t("确认") }} </el-button>
    <el-button size="large" :loading="loading" @click="cancel"> {{ $t("取消") }} </el-button>
  </div>
</template>

<script setup lang="ts">
import { createStaffPublic, userRegister } from "@/api/modules/staff";
import { getRoleList } from "@/api/modules/role";
import { ref, reactive, onMounted } from "vue";
import { ElNotification } from "element-plus";
import type { ElForm } from "element-plus";
import { Staff } from "@/typings/staff";
import { useRouter } from "vue-router";
import { getTimeState } from "@/utils";
import { Role } from "@/typings/role";
import { LOGIN_URL } from "@/config";
// import md5 from "md5";

const router = useRouter();
const roleList = ref<Role.Item[]>([]);

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  name: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const loading = ref(false);

const registerForm = reactive<Staff.Item>({} as Staff.Item);

const cancel = () => {
  router.replace(LOGIN_URL);
};

// login
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    // Object.assign(registerForm, { password: md5(registerForm.password!) });
    try {
      await userRegister(registerForm);
      router.replace(LOGIN_URL);
      ElNotification({
        title: getTimeState(),
        message: "注册成功",
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};

// const getAllRoleList = async () => {
//   const {
//     data: { list }
//   } = await getRoleList();
//   roleList.value = list;
// };

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
  // getAllRoleList();
});
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
