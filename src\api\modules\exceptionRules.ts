import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { DimensionsQuery } from "@/typings/dimensions";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}`;
/**
 * 列表
 * @param params
 */
export const getExceptionRules = (params: DimensionsQuery) => {
  return http.post<DimensionsQuery>(`${API_PREFIX}/exceptionRules/list`, params);
};
/**
 * 新增
 * @param params
 */
export const exceptionRulesAdd = (params: any) => {
  return http.post<DimensionsQuery>(`${API_PREFIX}/exceptionRules/insert`, params);
};
/**
 * 编辑
 * @param params
 */
export const exceptionRulesEdit = (params: any) => {
  return http.post<DimensionsQuery>(`${API_PREFIX}/exceptionRules/update`, params);
};
/**
 * 删除
 * @param params
 */
export const exceptionRulesRemove = (params: any) => {
  return http.post<DimensionsQuery>(`${API_PREFIX}/exceptionRules/del`, params);
};
