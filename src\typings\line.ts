export namespace Line {
  export interface Item {
    line: string;
    dept: string;
    staffNo: string;
    staffName: string;
    staffEmail: string;
    directorNo: string;
    directorName: string;
    directorEmail: string;
    deptId: string;
    plant: string;
    status: number;
    deleted: number;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface IQueryParams {
    startDate: string;
    endDate: string;
  }
}
