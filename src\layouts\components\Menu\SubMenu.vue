<template>
  <template v-for="subItem in menuList" :key="subItem.path">
    <el-sub-menu v-if="subItem.children?.length" :index="subItem.path">
      <template #title>
        <el-icon v-if="subItem.meta && subItem.meta.icon && subItem.meta.icon !== '#'">
          <component :is="subItem.meta.icon"></component>
        </el-icon>
        <el-tooltip class="box-item" effect="dark" :content="$t(subItem.meta?.title)" placement="right">
          <span class="sle">{{ $t(subItem.meta?.title) }}</span>
        </el-tooltip>
      </template>
      <SubMenu :menu-list="subItem.children" />
    </el-sub-menu>
    <el-menu-item v-else :index="subItem.path" @click="handleClickMenu(subItem)">
      <el-icon v-if="subItem.meta?.icon && subItem.meta.icon !== '#'">
        <component :is="subItem.meta?.icon"></component>
      </el-icon>
      <template #title>
        <el-tooltip class="box-item" effect="dark" :content="$t(subItem.meta?.title)" placement="right">
          <span class="sle">{{ $t(subItem.meta?.title) }}</span>
        </el-tooltip>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { isHttp } from "@/utils";
import { isEmpty, isNumber } from "@/utils/is";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";

defineProps<{ menuList: Menu.MenuOptions[] }>();

const router = useRouter();
const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (isNumber(subItem.path)) return ElMessage.error("模块暂未开发！");
  if (isHttp(subItem.path) && isEmpty(subItem.meta.isLink)) return ElMessage.error("模块暂未开发！");
  if (subItem.meta.isLink) return window.open(subItem.meta.isLink, "_blank");
  router.push(subItem.path);
};
</script>

<style lang="scss">
.el-sub-menu .el-sub-menu__title:hover {
  color: var(--el-menu-hover-text-color) !important;
  background-color: transparent !important;
}
.el-menu-item * {
  vertical-align: revert;
  .el-badge__content {
    border: 0;
  }
}
.el-menu--collapse {
  .is-active {
    .el-sub-menu__title {
      color: #ffffff !important;
      background-color: var(--el-color-primary) !important;
    }
  }
}
.el-menu-item {
  &:hover {
    color: var(--el-menu-hover-text-color);
  }
  &.is-active {
    color: var(--el-menu-active-color) !important;
    background-color: var(--el-menu-active-bg-color) !important;
    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 4px;
      content: "";
      background-color: var(--el-color-primary);
    }
  }
}
.vertical,
.classic,
.transverse {
  .el-menu-item {
    &.is-active {
      &::before {
        left: 0;
      }
    }
  }
}
.columns {
  .el-menu-item {
    &.is-active {
      &::before {
        right: 0;
      }
    }
  }
}
</style>
