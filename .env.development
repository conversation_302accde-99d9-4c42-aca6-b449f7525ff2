# 本地环境
VITE_USER_NODE_ENV = development

# 公共基础路径
VITE_PUBLIC_PATH = /

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = hash

# 打包时是否删除 console
VITE_DROP_CONSOLE = false

# 是否开启 VitePWA
VITE_PWA = false

# 开发环境接口地址
VITE_API_URL = "/prod-api"

VITE_GLOB_APP_TITLE = MMI

VITE_PREVIEW_BASE_URL = http://localhost:3000
#
VITE_PRIVATE_KEY = 'F5934C14C0F0193CA1D0344AF9C99739'

# 开发环境跨域代理，支持配置多个
# VITE_PROXY = [["/api","https://mock.mengxuegu.com/mock/629d727e6163854a32e8307e"]]


#VITE_PROXY = [["/api","http://*************:39985"], ["/admin-api", "http://*************:3011/admin-api"]]
VITE_PROXY = [["/prod-api", "https://supplier.wxmmi.com/prod-api"]]


# VITE_PROXY = [["/api","https://www.fastmock.site/mock/f81e8333c1a9276214bcdbc170d9e0a0"]]
# VITE_PROXY = [["/api-easymock","https://mock.mengxuegu.com"],["/api-fastmock","https://www.fastmock.site"]]
