<template>
  <div style="display: flex; align-items: center; justify-content: space-between; line-height: 0px">
    <template v-if="dateType === 'quarter'">
      <el-quarter-picker v-model="startDate" @change="handleChange" />
      <span>&nbsp;-&nbsp;&nbsp; </span>
      <el-quarter-picker v-model="endDate" @change="handleChange" />
    </template>
    <template v-else-if="dateType === 'week'">
      <el-week-picker v-model="startDate" @change="handleChange" />
      <span>&nbsp;-&nbsp;&nbsp;</span>
      <el-week-picker v-model="endDate" @change="handleChange" />
    </template>
    <template v-else>
      <el-date-picker
        style="width: 50%"
        v-model="startDate"
        :type="dateType"
        :placeholder="t(`开始`)"
        :format="dateFormat"
        :value-format="dateFormat"
        :disabled-date="disabledStartDate"
        @change="handleChange"
      />
      <span>&nbsp;-&nbsp;</span>
      <el-date-picker
        style="width: 50%"
        v-model="endDate"
        :type="dateType"
        :placeholder="t(`结束`)"
        :format="dateFormat"
        :value-format="dateFormat"
        :disabled-date="disabledEndDate"
        @change="handleChange"
      />
    </template>
  </div>
</template>

<script setup lang="ts" name="td">
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import ElQuarterPicker from "./ElQuarterPicker.vue";
import ElWeekPicker from "./ElWeekPicker.vue";

const { t } = useI18n();

const props = defineProps({
  resetSignal: Number,
  type: String,
  start: { type: String, default: "" },
  end: { type: String, default: "" }
});

const dateType = computed(() => props.type ?? "date");
const dateFormat = computed(() => {
  console.log("props:", props);
  if (dateType.value === "month") {
    return "YYYY-MM";
  } else if (dateType.value === "quarter") {
    return "YYYY-[Q]Q";
  } else if (dateType.value === "year") {
    return "YYYY";
  } else if (dateType.value === "week") {
    return "YYYY-WW";
  } else {
    return "YYYY-MM-DD";
  }
});

const emit = defineEmits(["dateRangeValue"]);

const startDate = ref(props.start || "");
const endDate = ref(props.end || "");

const disabledStartDate = (date: any) => {
  return endDate.value && date > new Date(endDate.value).getTime();
};

const disabledEndDate = (date: any) => {
  return startDate.value && date < new Date(startDate.value).getTime() - 24 * 60 * 60 * 1000;
};

const handleChange = () => {
  const selectedDates = [startDate.value ? startDate.value : "", endDate.value ? endDate.value : ""];
  if (selectedDates[0] && selectedDates[1] && selectedDates[0] > selectedDates[1]) {
    startDate.value = "";
    endDate.value = "";
    ElMessage.error({ message: t("开始日期不能大于结束日期") });
  }
  emit("dateRangeValue", selectedDates);
};

watch(
  () => props.start,
  newValue => {
    console.log("propValue 发生变化: ", props);
    endDate.value = props.end;
    startDate.value = props.start;
  },
  { deep: true, immediate: true }
);
watch(
  () => props.end,
  newValue => {
    console.log("propValue 发生变化: ", props);
    endDate.value = props.end;
    startDate.value = props.start;
  },
  { deep: true, immediate: true }
);

watch(
  () => props.resetSignal,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      startDate.value = "";
      endDate.value = "";
    }
  }
);
</script>
