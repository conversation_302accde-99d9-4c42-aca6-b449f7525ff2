<script setup lang="ts">
import { downloadFile } from "@/utils/download";

const download = async (url: string, name: string) => {
  downloadFile(url, name);
};

const props = defineProps<{
  url: string;
  name: string;
}>();
</script>

<template>
  <el-link type="primary" v-if="props.url" target="_blank" @click="download(url, name)">
    {{ name }}
  </el-link>
  <span v-else-if="!props.url && name"> {{ name }} </span>
  <span v-else> - </span>
</template>
