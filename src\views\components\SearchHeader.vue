<template>
  <SearchForm :search="_search" :reset="_reset" :columns="searchColumns" :search-col="searchCol" :search-param="searchParam" />
</template>

<script setup lang="ts" name="ProTable">
import { ref, provide, unref, computed } from "vue";
import { BreakPoint } from "@/components/Grid/interface";
import { ColumnProps } from "@/components/ProTable/interface";
import SearchForm from "@/components/SearchForm/index.vue";
import { handleProp } from "@/utils";

export interface ProTableProps {
  columns: ColumnProps[];
  searchCol?: number | Record<BreakPoint, number>;
}

const searchParam = ref<{ [key: string]: any }>({});
const props = withDefaults(defineProps<ProTableProps>(), {
  columns: () => [],
  searchCol: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 })
});

const tableColumns = computed<ColumnProps[]>(() => props.columns);
const flatColumns = computed(() => flatColumnsFunc(tableColumns.value));
const enumMap = ref(new Map<string, { [key: string]: any }[]>());

const setEnumMap = async ({ prop, enum: enumValue }: ColumnProps) => {
  if (!enumValue) return;
  if (enumMap.value.has(prop!) && (typeof enumValue === "function" || enumMap.value.get(prop!) === enumValue)) return;
  if (typeof enumValue !== "function") return enumMap.value.set(prop!, unref(enumValue!));
  enumMap.value.set(prop!, []);
  const { data } = await enumValue();
  enumMap.value.set(prop!, data);
};

provide("enumMap", enumMap);

const flatColumnsFunc = (columns: ColumnProps[], flatArr: ColumnProps[] = []) => {
  columns.forEach(async col => {
    if (col._children?.length) flatArr.push(...flatColumnsFunc(col._children));
    flatArr.push(col);
    col.isShow = col.isShow ?? true;
    col.isFilterEnum = col.isFilterEnum ?? true;
    await setEnumMap(col);
  });
  return flatArr.filter(item => !item._children?.length);
};

const searchColumns = computed(() => {
  return flatColumns.value
    ?.filter(item => item.search?.el || item.search?.render)
    .sort((a, b) => a.search!.order! - b.search!.order!);
});

searchColumns.value?.forEach((column, index) => {
  column.search!.order = column.search?.order ?? index + 2;
  const key = column.search?.key ?? handleProp(column.prop!);
  const defaultValue = column.search?.defaultValue;
  if (defaultValue !== undefined && defaultValue !== null) {
    searchParam.value[key] = defaultValue;
  }
});

const emit = defineEmits<{
  search: any;
  reset: any;
}>();

const _search = () => {
  console.log("search", searchParam.value);
  emit("search", searchParam.value);
};

const _reset = () => {
  searchParam.value = {};
  console.log("reset", searchParam.value);
  emit("reset", searchParam.value);
};
</script>

<style>
.nowrap-cell {
  white-space: nowrap;
}
</style>
