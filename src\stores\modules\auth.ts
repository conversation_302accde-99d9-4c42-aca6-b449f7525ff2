import { defineStore } from "pinia";
import { AuthState } from "@/stores/interface";
import { getAuthButtonList<PERSON>pi, getAuthMenuListApi } from "@/api/modules/login";
import { getFlatMenuList, getShowMenuList, getAllBreadcrumbList, handleTree } from "@/utils";
import { data } from "@/assets/json/authMenuList.json";
import { isEmpty } from "@/utils/is";
import { getAppAllList, getAppList } from "@/api/modules/app";
import { APP_CODE } from "@/config";
import { getUserInfo } from "@/api/modules/auth";
import { log } from "echarts/types/src/util/log";
// import { Menu } from "@/typings/menu";
// import MenuRoute = Menu.MenuRoute;

const formatMenu = (menu: any[], parent?: any) => {
  for (let i = 0; i < menu.length; i++) {
    delete menu[i].redirect;
    menu[i].component = "/" + menu[i].component;
    if (menu[i].meta) {
      menu[i].meta.isLink = menu[i].meta?.link;
      menu[i].meta.isHide = menu[i].hidden;
      if (menu[i].meta.icon === "#") {
        menu[i].meta.icon = "";
      }
    }
    if (parent) {
      menu[i].path = parent.path + "/" + menu[i].path;
    }
    if (Array.isArray(menu[i].children)) {
      formatMenu(menu[i].children, menu[i]);
    }
  }
};

export const useAuthStore = defineStore({
  id: "geeker-auth",
  state: (): AuthState => ({
    // 按钮权限列表
    authButtonList: [],

    // 菜单权限列表
    authMenuList: [],
    // 当前页面的 router name，用来做按钮权限筛选
    routeName: "",
    menuId: 0,
    workflowId: 0
  }),
  getters: {
    // 按钮权限列表
    authButtonListGet: state => state.authButtonList,
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: state => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: state => getShowMenuList(state.authMenuList),
    // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
    flatMenuListGet: state => getFlatMenuList(state.authMenuList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList)
  },
  actions: {
    // Get AuthButtonList
    async getAuthButtonList() {
      // const { data } = await getAuthButtonListApi();
      // this.authButtonList = data;

      const data = await getUserInfo();
      this.authButtonList = data.permissions;
    },
    // Get AuthMenuList
    async getAuthMenuList() {
      const res = await getAuthMenuListApi();
      /**
       * @description 📚 路由参数配置简介
       * @param path ==> 路由菜单访问路径
       * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
       * @param redirect ==> 路由重定向地址
       * @param component ==> 视图文件路径
       * @param meta ==> 路由菜单元信息
       * @param meta.icon ==> 菜单和面包屑对应的图标
       * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
       * @param meta.activeMenu ==> 当前路由为详情页时，需要高亮的菜单
       * @param meta.isLink ==> 路由外链时填写的访问地址
       * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
       * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
       * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
       * @param meta.isKeepAlive ==> 当前路由是否缓存
       * */
      let menuList = res.data;
      //   .map(item => {
      //   console.log(item, "item");
      //   item.meta.isLink = item.meta.link;
      //   item.meta.isHide = item.hidden;
      //
      //   if (item.meta.icon === "#") {
      //     item.meta.icon = "";
      //   }
      //   return item;
      // });
      formatMenu(menuList, null);
      // const { data: appList } = await getAppAllList({ showMenu: "1" });

      // const newAppList = [] as any;
      // menuList.forEach(item => {
      //   // newAppList.push({
      //   //   id: item.id,
      //   //   path: isEmpty(item.url) ? item.id : item.url,
      //   //   component: item.url,
      //   //   parentId: item.parentId,
      //   //   code: item.code,
      //   //   meta: {
      //   //     icon: item.icon,
      //   //     title: item.name,
      //   //     isLink: isEmpty(item.url) ? "" : item.url,
      //   //     isHide: false,
      //   //     isFull: false,
      //   //     isAffix: true,
      //   //     isKeepAlive: false
      //   //   }
      //   // });
      //   delete item.redirect;
      //   newAppList.push({
      //     ...item
      //   });
      // });

      // console.log(newAppList, "menuListmenuList");

      // const data = handleTree(menuList);

      // function addTreeMenu(menuList: any[], addTreeList: any[], code: string) {
      //   for (const menu of menuList) {
      //     if (!isEmpty(menu.children)) {
      //       addTreeMenu(menu.children, addTreeList, code);
      //     }
      //     if (menu.code === code) {
      //       menu.children = addTreeList;
      //       break;
      //     }
      //   }
      // }

      // this.authButtonList = buttons;
      // const authTreeMenu = handleTree(newAppList);
      // const modulesMap: Record<string, any[]> = {};
      // authTreeMenu.forEach(item => {
      //   const code = item.meta.appCode!;
      //   modulesMap[code] = modulesMap[code] || [];
      //   modulesMap[code].push(item);
      // });

      // data.forEach(item => {
      //   if (item.code === "basic-settings") {
      //     addTreeMenu(data, modulesMap[item.code], item.code);
      //   } else {
      //     item.children?.forEach((child: { code: string }) => {
      //       if (child.code) {
      //         if (!["car", "score", "8d", "dfx"].includes(child.code)) {
      //           addTreeMenu(data, modulesMap[child.code], child.code);
      //         }
      //       }
      //     });
      //   }
      // });
      // console.log(menuList);
      this.authMenuList = menuList;
    },
    // Set RouteName
    async setRouteName(name: string) {
      this.routeName = name;
      this.menuId = this.flatMenuListGet.find(item => item.name === name)?.id ?? 0;
    }
  }
});
