import html2Canvas from "html2canvas";
import { ElMessage } from "element-plus";
import { getMessage } from "@/utils";
import { upload } from "@/api/modules/common";

export async function generateImage(elementId: string): Promise<string | null> {
  try {
    // 动态插入样式，确保图片生成时显示正确
    const style = document.createElement("style");
    document.head.appendChild(style);
    style.sheet?.insertRule("body > div:last-child img { display: inline-block; }");

    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error("Element not found");
    }

    const elementWidth = element.scrollWidth;
    const elementHeight = element.scrollHeight;

    // 使用 html2canvas 生成图片
    const canvas = await html2Canvas(element, {
      scale: 2,
      useCORS: true,
      scrollX: 0,
      scrollY: 0,
      width: elementWidth,
      height: elementHeight,
      windowWidth: elementWidth,
      windowHeight: elementHeight,
      // 忽略 el-header，其中的头像会拖慢生成速度
      ignoreElements: el => {
        return el.classList.contains("el-header");
      }
    });

    style.remove(); // 移除样式

    const blob = await new Promise<Blob | null>(resolve => canvas.toBlob(blob => resolve(blob), "image/jpeg", 1.0));

    if (!blob) {
      ElMessage.error({ message: getMessage("图片生成失败") });
      return null;
    }

    let formData = new FormData();
    formData.append("file", blob, "publish.jpg");

    const { success, data } = await upload(formData);
    if (!success) {
      ElMessage.error({ message: getMessage("上传图片失败") });
      return null;
    }
    return data.url;
  } catch (error) {
    ElMessage.error({ message: error || getMessage("图片生成和上传过程中出错") });
    return null;
  }
}
