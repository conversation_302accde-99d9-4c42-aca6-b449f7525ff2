<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" :data-callback="dataCallback">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'dict:add'" type="primary" @click="openDictModal('新增')">{{ $t("新增") }}</el-button>
        <el-button v-auth="'dict:edit'" type="primary" @click="openDictModal('编辑')">{{ $t("编辑") }}</el-button>
        <el-button type="primary" @click="backPage">{{ $t("返回上一页") }}</el-button>
      </template>
      <template #dictType="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.dictType }}</el-link>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button
          v-auth="'dict:delete'"
          :disabled="!isSelected"
          type="danger"
          plain
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <DictModal ref="dictModalRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import { createDataDict, delDictData, editDataDict, getDictDataList, getDictDataLists } from "@/api/modules/dict";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import DictModal from "./components/DictModal.vue";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
import { Dict, IDataItem } from "@/typings/dict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs } from "vue";
import { useAuthStore } from "@/stores/modules/auth";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

const router = useRouter();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const dictModalRef = ref<InstanceType<typeof DictModal> | null>(null);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Dict.IDataItem & ReqPage>({ pageNum: 1, pageSize: 10 } as Dict.IDataItem & ReqPage);

const { common_status } = useDict("common_status");
const { t } = useI18n();
const pageButtons = ["dict:edit", "dict:delete"];

const auth = useAuthStore();
const route = useRoute();
const dictType = route.query.dictType as string;
// 表格配置项
const columns = reactive<ColumnProps<Dict.ITypeItem>[]>([
  { type: "selection", fixed: "left", width: 60 },
  { type: "index", label: "序号", width: 80 },
  { prop: "dictLabel", label: "字典键名", search: { el: "input" } },
  { prop: "dictValue", label: "字典键值", search: { el: "input" } },
  { prop: "remark", label: "说明" },
  {
    prop: "status",
    label: "状态",
    tag: false,
    enum: common_status,
    search: { el: "select" },
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "createdTime",
    label: "创建时间",
    render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  }
  // ...(visibleOperationCol(auth.authButtonList, pageButtons)
  //   ? [
  //       {
  //         prop: "operation",
  //         label: "操作",
  //         width: getOperationColWidth(auth.authButtonList, pageButtons),
  //         fixed: "right"
  //       }
  //     ]
  //   : [])
]);

const dataCallback = (data: ResPage<Dict.ITypeItem>) => {
  return {
    list: data.list?.map(item => {
      item.id = item.dictCode;
      return item;
    }),
    total: data.total
  };
};

const getTableList = (params: any) => {
  return getDictDataLists({ ...params, dictType });
};

const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(delDictData, ids.join(","), t("删除所选信息"));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openDictModal = (title: string, row: Partial<Dict.Detail> = {}) => {
  if (title !== "新增") {
    check();
  }
  const form = title === "新增" ? { dictType } : { ...currentRow.value, dictType };
  const params = {
    title,
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: title === "新增" ? createDataDict : title === "编辑" ? editDataDict : void 0,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { status: "0" });
  dictModalRef.value?.acceptParams(params);
};

const backPage = () => {
  router.go(-1);
};
const handleView = () => {
  router.push({
    path: "/system/dict-data"
  });
};
</script>
