export interface ExceptionManageItem {
  /**
   * 附件名称
   */
  attachment_name: string;
  /**
   * 附件URL
   */
  attachment_url: string;
  create_time: string;
  creator_id: number;
  /**
   * 当前节点ID
   */
  current_node_id: number;
  /**
   * 当前节点
   */
  current_node_name: string;
  /**
   * 天数
   */
  day: string;
  /**
   * 异常描述
   */
  exception_description: string;
  /**
   * 异常类型（CIP/8D）
   */
  exception_type: string;
  id: number;
  /**
   * 产品
   */
  product: number;
  /**
   * 报告编号
   */
  report_no: string;
  /**
   * 进行中
   */
  report_status: number;
  /**
   * 责任人
   */
  responsible_person: number;
  /**
   * 来源（1=IQA，2=QCAN，3=其他）
   */
  source: number;
  /**
   * 来源编号
   */
  source_no: string;
  /**
   * 提交时间
   */
  submit_time: string;
  /**
   * 提交人
   */
  submitter_by: string;
  /**
   * 提交人工号
   */
  submitter_no: string;
  /**
   * 供应商
   */
  supplier: number;
  /**
   * 异常标题
   */
  title: string;
  update_time: string;
  updater_id: number;
  rejected?: string;
  [property: string]: any;
}
export interface ExceptionManageForm {
  /**
   * 附件名称
   */
  attachmentName?: string;
  /**
   * 附件URL
   */
  attachmentUrl?: string;
  /**
   * 异常描述
   */
  exceptionDescription?: string;
  /**
   * 异常类型
   */
  exceptionType?: string;
  /**
   * ID 编号
   */
  id?: number;
  /**
   * 产品
   */
  product?: string;
  /**
   * 责任人
   */
  responsibleBy?: string;
  /**
   * 来源（IQA，QCAN，其他）
   */
  source?: string;
  /**
   * 来源编号
   */
  sourceNo?: string;
  /**
   * 提交人
   */
  submitterBy?: string;
  /**
   * 提交人工号
   */
  submitterNo?: string;
  /**
   * 供应商
   */
  supplierName?: string;
  /**
   * 异常标题
   */
  title?: string;
  [property: string]: any;
}
export namespace ExceptionManageQuery {
  export interface Item {
    /**
     * 附件名称
     */
    attachmentName?: string;
    /**
     * 附件URL
     */
    attachmentUrl?: string;
    /**
     * 异常描述
     */
    exceptionDescription?: string;
    /**
     * 异常类型
     */
    exceptionType?: string;
    /**
     * ID 编号
     */
    id?: number;
    /**
     * 产品
     */
    product?: string;
    /**
     * 责任人
     */
    responsibleBy?: string;
    /**
     * 来源（IQA，QCAN，其他）
     */
    source?: string;
    /**
     * 来源编号
     */
    sourceNo?: string;
    /**
     * 提交人
     */
    submitterBy?: string;
    /**
     * 提交人工号
     */
    submitterNo?: string;
    /**
     * 供应商
     */
    supplierName?: string;
    /**
     * 异常标题
     */
    title?: string;
  }
  interface IQueryParams {
    startDate: string;
    endDate: string;
  }
}
