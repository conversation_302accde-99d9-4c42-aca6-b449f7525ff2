import { defineStore } from "pinia";
import { LanguageState } from "@/stores/interface";
import { getAllLanguageList } from "@/api/modules/language";
import piniaPersistConfig from "../helper/persist";
import { languages } from "@/languages/data";

const codes: any = {
  "en-US": {
    code: "en",
    name: "English"
  },
  "zh-CN": {
    code: "zh",
    name: "中文"
  }
};
export const useLanguageStore = defineStore({
  id: "language",
  state: (): LanguageState => ({
    languageCode: "zh",
    languageCodeList: []
  }),
  getters: {
    languageData: state => state.languageCodeList.find(v => v.code === state.languageCode)?.langData || [],
    code: state => state.languageCode
  },
  actions: {
    async getLanguageCodeList() {
      const { data } = await getAllLanguageList();

      this.languageCodeList = data.map(item => {
        item.name = codes[item.code].name;
        item.code = codes[item.code].code;

        return item;
      });
    },
    // Set RouteName
    async setLanguageCode(code: string) {
      this.languageCode = code;
    },
    async getLanguageCodeData(code: string) {
      const message = this.languageCodeList.find(v => v.code === code)?.langData || [];
      return message.reduce((prev, curr) => {
        return { ...prev, [curr.label.replace(`\\n`, `\n`)]: curr.value };
      }, {});
    }
  },
  persist: piniaPersistConfig("language")
});

export default useLanguageStore;
