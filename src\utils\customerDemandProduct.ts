import { ref } from "vue";
import { getCustomerDemandProductInfoList } from "@/api/modules/customer-demand/customer_demand_productInfo";
import { CustomerDemandProductInfo } from "@/typings/customer-demand/customer_demand_productInfo";

export const customerDemandProductList = ref<CustomerDemandProductInfo.Item[]>([]);
export const getCustomerDemandProductList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getCustomerDemandProductInfoList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      customerDemandProductList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
