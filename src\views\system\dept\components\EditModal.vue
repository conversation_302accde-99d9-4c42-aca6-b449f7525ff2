<template>
  <el-dialog v-model="visible" width="40%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :validate-on-rule-change="false"
    >
      <el-row>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item :label="$t('绑定应用')" prop="appCode">-->
        <!--            <el-select v-model="form.appCode">-->
        <!--              <el-option v-for="{ name, code } of appList" :key="code" :label="$t(name)" :value="code" />-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="12" v-if="!(form.parentId == 0 || !form.parentId)">
          <el-form-item :label="$t('上级部门')" prop="parentId">
            <el-tree-select
              v-model="form.parentId"
              :data="menuList"
              :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
              :placeholder="$t('请选择')"
              value-key="deptId"
              check-strictly
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-col :span="12">
            <el-form-item :label="$t('部门名称')" prop="deptName">
              <el-input v-model="form.deptName" :placeholder="$t(`请填写`)" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('显示顺序')" prop="orderNum">
              <el-input v-model="form.orderNum" type="number" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('负责人')" prop="leader">
              <el-input v-model="form.leader" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('联系电话')" prop="phone">
              <el-input v-model="form.phone" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('邮箱')" prop="email">
              <el-input v-model="form.email" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-form-item :label="$t('状态')" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{ $t(label) }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="MenuModal">
import { ElMessage, FormInstance } from "element-plus";
import { ref, toRefs, reactive, computed } from "vue";
import { Menu, MenuItem } from "@/typings/menu";
// import { getMenuList } from "@/api/modules/menu";
// import SelectIcon from "@/components/SelectIcon/index.vue";
import { isEmpty } from "@/utils/is";
import { Dict } from "@/typings/dict";
// import { useDict } from "@/hooks/useDict";
// import { WorkFlow } from "@/typings/work-flow";
// import { getWorkFlowList } from "@/api/modules/work-flow";
import { App } from "@/typings/app";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { StatusTypes } from "@/utils/dicts/type";
import { ResultData } from "@/api/interface";
// import { common_yes_no } from "@/utils/dicts";
// const { sys_yes_no, sys_show_hide } = useDict("sys_yes_no", "sys_show_hide");
import { handleTree } from "@/utils";
import { getDeptList } from "@/api/modules/dept";
import { Dept } from "@/typings/dept";
const { t } = useI18n();
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Dept.Item>;
  common_status: Dict.IDataItem[];
  appList: App.Item[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const typeOptions = [
  { label: t("菜单"), value: "M" },
  { label: t("按钮"), value: "B" }
];

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "180px"));

// const { common_yes_no } = useDict("common_yes_no");
// const common_yes_no = ref([]);
const workflowList = ref<any[]>([]);
const menuList = ref<MenuItem>();
const visible = ref(false);

const formRef = ref<FormInstance>();
const state = reactive<IState>({
  isView: false,
  title: "",
  common_status: [],
  appList: [],
  form: {}
});

const { form, title, isView, common_status, appList } = toRefs(state);

const typeName = computed(() => typeOptions.find(item => item.value === form.value.menuType)?.label);

const rules = computed(() => ({
  deptName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  // appCode: [{ required: true, message: `请选择应用名称`, trigger: "change" }],
  orderNum: [{ required: true, message: t("请填写"), trigger: "blur" }],
  parentId: [{ required: true, message: t("请选择"), trigger: "blur" }]
}));

const setVisible = (val: boolean) => {
  visible.value = val;
};

// const _getWorkFlowList = async () => {
// const {
//   data: { list }
// } = await getWorkFlowList();
// workflowList.value = list;
// };

const getMenuAllList = async () => {
  let { data } = await getDeptList();
  menuList.value = handleTree(data, "deptId", "parentId", "children", true, "orderNum");
};

// 接收父组件传过来的参数
// isCache;
// isFrame;
//
// menuName: menuType;
// orderNum;
// parentId;
// path;
// status;
// visible;

const acceptParams = async (params: {
  form: {
    deptName: string;
    leader: string;
    phone: string;
    email: string;
    parentId: number;
  };
  common_status: StatusTypes[];
  isView: boolean;
  getTableList: (() => Promise<void>) | undefined;
  // appList: Array<UnwrapRefSimple<App.Item>>;
  api: (data: Menu.Item) => Promise<ResultData<unknown>>;
  title: string;
}) => {
  await getMenuAllList();
  // _getWorkFlowList();
  if (isEmpty(params.form.parentId)) {
    params.form.parentId = 0;
  }
  // console.log(params, "leaderleaderleaderleaderleaderleader");

  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
