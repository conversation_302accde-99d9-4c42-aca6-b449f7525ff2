export interface DimensionsQuery {
  endDate?: string;
  model: string;
  parameter: string;
  product: string;
  startDate?: string;
  station: string;
  supplier: string;
  workOrderNo: string;
}
/**
 * core_ipqa，IPQA记录表
 */
export interface DimensionsItem {
  /**
   * 创建时间
   */
  create_time: Date;
  /**
   * 创建人ID
   */
  creator_id: number;
  /**
   * 日期（格式：YYYY-MM-DD）
   */
  date?: Date;
  /**
   * ID
   */
  id: number;
  /**
   * LSL
   */
  LSL?: string;
  /**
   * Measure Data
   */
  measure_data?: string;
  /**
   * 型号
   */
  model?: number | string;
  /**
   * Parameter
   */
  parameter?: string;
  /**
   * 产品
   */
  product?: string;
  /**
   * Remark
   */
  remark?: string;
  /**
   * Result
   */
  result?: string;
  /**
   * SN
   */
  sn?: string;
  /**
   * station
   */
  station?: string;
  /**
   * 供应商
   */
  supplier?: string;
  /**
   * 更新时间
   */
  update_time: Date;
  /**
   * 修改人ID
   */
  updater_id: number;
  /**
   * USL
   */
  USL?: string;
  /**
   * 工单号
   */
  work_order_no?: string;
  [property: string]: any;
}
export namespace DimensionsQuery {
  export interface Item {
    /**
     * 创建时间
     */
    create_time: Date;
    /**
     * 创建人ID
     */
    creator_id: number;
    /**
     * 日期（格式：YYYY-MM-DD）
     */
    date?: Date;
    /**
     * ID
     */
    id: number;
    /**
     * LSL
     */
    LSL?: string;
    /**
     * Measure Data
     */
    measure_data?: string;
    /**
     * 型号
     */
    model?: number;
    /**
     * Parameter
     */
    parameter?: string;
    /**
     * 产品
     */
    product?: string;
    /**
     * Remark
     */
    remark?: string;
    /**
     * Result
     */
    result?: string;
    /**
     * SN
     */
    sn?: string;
    /**
     * station
     */
    station?: string;
    /**
     * 供应商
     */
    supplier?: string;
    /**
     * 更新时间
     */
    update_time: Date;
    /**
     * 修改人ID
     */
    updater_id: number;
    /**
     * USL
     */
    USL?: string;
    /**
     * 工单号
     */
    work_order_no?: string;

    [property: string]: any;
  }
  interface IQueryParams {
    startDate: string;
    endDate: string;
  }
}
