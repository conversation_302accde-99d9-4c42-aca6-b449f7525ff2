<template>
  <el-dialog v-model="visible" width="40%" :title="$t('添加快捷入口')">
    <el-form ref="formRef" label-width="110px" label-suffix=" :" :model="form">
      <el-row>
        <el-col :span="18">
          <el-form-item :label="$t('菜单')" prop="menuIds">
            <el-tree-select
              v-model="selectIds"
              :data="authMenuList"
              :props="{ label: 'title', value: 'id', children: 'children' }"
              multiple
              value-key="id"
              check-strictly
              :render-after-expand="false"
              show-checkbox
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="QuickMenuModal">
import { ref, computed, watchEffect } from "vue";
import { useAuthStore } from "@/stores/modules/auth";
import { setQuickEntry } from "@/api/modules/user";
import { App } from "@/typings/app";
import { Home } from "@/typings/home";
import { isEmpty } from "@/utils/is";

interface IEmits {
  (e: "submitSuccess"): void;
}

interface IProps {
  menuIds: number[];
  admin: App.Item;
}

const emits = defineEmits<IEmits>();

const props = defineProps<IProps>();

const menuIds = computed(() => props.menuIds);
const admin = computed(() => props.admin);

const authStore = useAuthStore();
const selectIds = ref<number[]>(menuIds.value);
const authMenuList = computed(() => authStore.authMenuList);
const authMenuFlatList = computed(() => authStore.flatMenuListGet);

const visible = ref(false);

const setEntries = () => {
  if (isEmpty(selectIds.value)) return (form.value.entries = []);
  const entries = authMenuFlatList.value
    .filter(item => selectIds.value.includes(item.id))
    .map(({ title, path, id: entryId }) => ({ title, path, entryId }));
  form.value.entries = entries;
};

const initForm = () => {
  selectIds.value = menuIds.value;
  form.value.appCode = admin.value.code;
  setEntries();
};

const form = ref<Home.ISetQuickMenuParams>({ appCode: admin.value.code, entries: [] });

const setVisible = (value: boolean) => {
  if (value) {
    initForm();
  }
  visible.value = value;
};

const handleSubmit = async () => {
  try {
    await setQuickEntry(form.value);
    setVisible(false);
    emits("submitSuccess");
  } catch (error) {}
};

watchEffect(() => {
  selectIds.value;
  setEntries();
});

defineExpose({
  setVisible
});
</script>
