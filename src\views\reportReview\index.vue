<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <!--        <el-button type="primary" @click="handleModalOperation('新增')">{{ $t("新增") }}</el-button>-->
        <!--        <el-button type="primary" @click="openRoleModal('编辑')">{{ $t("提醒") }}</el-button>-->
        <el-button v-auth="'reportReview:review'" type="primary" @click="handleExceptionInfo('review')">
          {{ $t("审批") }}
        </el-button>
      </template>
      <template #reportNo="{ row }">
        <el-link type="primary" @click="handleExceptionInfo('view', row)">{{ row.reportNo }}</el-link>
      </template>
      <!--      <template #footerBtn="{ selectedListIds, isSelected }">-->
      <!--        <el-button-->
      <!--          v-auth="'role:delete'"-->
      <!--          type="danger"-->
      <!--          plain-->
      <!--          :disabled="!isSelected"-->
      <!--          @click="batchDelete(selectedListIds as number[])"-->
      <!--        >-->
      <!--          {{ $t("批量删除") }}-->
      <!--        </el-button>-->
      <!--      </template>-->
      <!-- 表格操作 -->
      <!--      <template #operation="{ row }">-->
      <!--&lt;!&ndash;        <el-button v-auth="'exception:edit'" type="primary" link @click="handleModalOperation('修改', row)">修改</el-button>&ndash;&gt;-->
      <!--        <el-button v-auth="'exception:remove'" type="danger" link @click="handleRemove(row)">删除</el-button>-->
      <!--      </template>-->
    </ProTable>
    <!--    <handleModal ref="handleModalRef" />-->
    <exceptionInfoModal ref="exceptionInfoModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import {
  getExceptionManage,
  exceptionManageDelete,
  exceptionManageExport,
  exceptionManageAdd,
  exceptionManageSave,
  exceptionReportsMyList
} from "@/api/modules/exception-manage";
import { ExceptionManageItem, ExceptionManageQuery } from "@/typings/exception-manage";
import { ref, reactive, onMounted, nextTick } from "vue";
import { isArray, isEmpty } from "@/utils/is";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { downloadFileByName } from "@/utils/download";
import exceptionInfoModal from "@/views/components/exceptionInfoModal.vue";
import { getOperationColWidth, visibleOperationCol } from "@/utils";
import { useAuthStore } from "@/stores/modules/auth";
import { useDict } from "@/hooks/useDict";
import DateRange from "@/views/components/DateRange.vue";
import { Supplier } from "@/typings/supplier";
import { Staff } from "@/typings/staff";
import { formatParams } from "@/utils/util";
import { getSupplierAll } from "@/api/modules/supplier";
import { getPublicUser } from "@/api/modules/staff";
import { useI18n } from "vue-i18n";

const exceptionInfoModalRef = ref<InstanceType<typeof exceptionInfoModal> | null>();
const proTable = ref<ProTableInstance>();

const { product_names, report_exception_type, report_node_type_8d, common_status, report_status } = useDict(
  "product_names",
  "report_exception_type",
  "report_node_type_8d",
  "common_status",
  "report_status"
);

const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();
const initParam = reactive({
  pageSize: 10,
  pageNum: 1,
  condition: {
    startDateTime: "",
    endDateTime: "",
    reportNo: "",
    supplier: "",
    reportStatus: "",
    product: "",
    createBy: "",
    title: "",
    exceptionType: ""
  }
});

const pageButtons = ["exception:edit", "exception:remove"];
const auth = useAuthStore();

const supplierList = ref<Supplier.Item[]>([]);
const userList = ref<Staff.Item[]>([]);
const columns = reactive<ColumnProps<ExceptionManageItem>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "reportNo",
    label: "异常编号",
    search: { el: "input", render: () => <el-input v-model={initParam.reportNo} clearable placeholder={t("请输入")} /> },
    width: 200
  },
  {
    prop: "title",
    label: "异常标题",
    search: { el: "input", render: () => <el-input v-model={initParam.title} clearable placeholder={t("请输入")} /> },
    width: 300
  },
  {
    prop: "supplierName",
    label: "供应商",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "product",
    label: "产品名称",
    enum: product_names,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.product} placeholder={t("请选择")} clearable>
            {product_names.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "reportStatus",
    label: "状态",
    enum: report_status,
    width: 150,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.reportStatus} placeholder={t("请选择")} clearable>
            {report_status.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "exceptionType",
    label: "异常类型",
    enum: report_exception_type,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.exceptionType} placeholder={t("请选择")} clearable>
            {report_exception_type.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "sourceNo", label: "NCMR", width: 120, width: 200 },
  { prop: "currentNodeName", label: "当前节点", width: 120, width: 200 },
  { prop: "day", label: "已用时(天)", width: 120, width: 200 },
  { prop: "responsibleBy", label: "责任人", width: 120, width: 200 },
  {
    prop: "createBy",
    label: "创建人",
    enum: userList.value,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.createBy} placeholder={t("请选择")} clearable>
            {userList.value.map(item => (
              <el-option key={item.id} label={item.nickName} value={item.nickName} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 200,
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  }
]);
let queryParams = reactive<ExceptionManageQuery.IQueryParams>({} as ExceptionManageQuery.IQueryParams);
const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  // const { pageNum, pageSize, ...condition } = params;
  //
  // if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
  //   condition.startDate = filterDate.value[0];
  //   condition.endDate = filterDate.value[1];
  // }
  //
  // queryParams = reactive(condition);
  const result = formatParams(initParam, params, filterDate.value);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return exceptionReportsMyList(result);
};

// 批量删除用户信息
const batchDelete = async (id: number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(exceptionManageDelete, { ids }, t("删除所选信息"));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

/**
 * 删除
 * @param row
 */
const handleRemove = async (row: ExceptionManageItem) => {
  await useHandleData(exceptionManageDelete, { ids: [row.id] }, t("删除所选数据"));
  proTable.value?.getTableList();
};

/**
 * 导出
 * @param row
 */
const handleExport = async (row: ExceptionManageItem) => {
  await downloadFileByName(exceptionManageExport, initParam);
};

const handleExceptionInfo = (type: string, row?: ExceptionManageItem) => {
  let paramsForm = row || {};
  let title = "查看";
  let isView = true;
  if (type !== "view") {
    check();
    paramsForm = currentRow.value;
    title = "异常审批";
    isView = false;
  }
  const params = {
    title,
    isView,
    form: paramsForm,
    getTableList: proTable.value?.getTableList,
    handleType: type
  };
  exceptionInfoModalRef.value?.acceptParams(params);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};
const getStaffListAll = async () => {
  const { data } = await getPublicUser({ pageNum: 1, pageSize: 999 });
  userList.value = data.list;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};
onMounted(async () => {
  await getSupplierData();
  await getStaffListAll();
});
</script>
