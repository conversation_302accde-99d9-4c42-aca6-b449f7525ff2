import { getMessage } from ".";
import { API_PREFIX } from "@/api/config/servicePort";
import http from "@/api";

export function downloadFile(url: string, name: string) {
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      const link = document.createElement("a");
      const href = URL.createObjectURL(blob);
      link.setAttribute("href", href);
      link.setAttribute("download", name);
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    })
    .catch(error => console.error(getMessage("下载文件出错") + ":", error));
}

export async function downloadFileByName(fun: Function, params: any) {
  const data = await fun(params);
  // if (!params.delete) params.delete = false;
  http
    .downloadGet(`${API_PREFIX}/common/download?fileName=${data.msg}&delete=true`)
    // .then(response => response.blob())
    .then(blob => {
      const link = document.createElement("a");
      const href = URL.createObjectURL(blob);
      link.setAttribute("href", href);
      link.setAttribute("download", data?.data?.attachmentName || data.msg);
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    })
    .catch(error => console.error(getMessage("下载文件出错") + ":", error));
}
export async function downloadTemplateSourceFile(fun: Function, params: any) {
  const { data } = await fun(params);
  // if (!params.delete) params.delete = false;
  http
    .downloadGet(`${API_PREFIX}/common/download/resource?resource=${data.attachmentUrl}&delete=true`)
    // .then(response => response.blob())
    .then(blob => {
      const link = document.createElement("a");
      const href = URL.createObjectURL(blob);
      link.setAttribute("href", href);
      link.setAttribute("download", data.attachmentName);
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    })
    .catch(error => console.error(getMessage("下载文件出错") + ":", error));
}
export async function downloadTemplateFie(fun: Function, params: any) {
  const { data } = await fun(params);
  const link = document.createElement("a");
  const href = data.attachmentUrl;
  link.setAttribute("href", href);
  link.setAttribute("target", "_blank");
  link.setAttribute("download", data.attachmentName);
  link.style.display = "none";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
}
