export namespace Menu {
  export interface Item {
    menuId: number;
    id: number;
    appCode: string;
    parentId: number; // 上级菜单id
    parentName?: string; // 上级名称 可选
    title: string; // 菜单名称 ｜ 按钮名称
    type: string; //类型  M: 菜单 B: 按钮
    name: string; // 路由名称
    path: string; // 路由地址
    redirect?: string; // 重定向到
    component: string; // 组件路径
    isFull?: boolean; // 是否全屏
    isLink?: boolean; // 是否外链
    isHide?: boolean; // 是否隐藏
    isAffix?: boolean; // 是否吸附
    isKeepAlive?: boolean; // 是否缓存
    icon?: string; // 图标
    perms: string; // 菜单 ｜ 按钮权限标识 可为空串
    sort: number; // 排序
    createdTime: string;
    status: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    children?: Item[];
    workflowId?: number;
  }

  export interface MenuRoute {
    hidden: boolean;
    id: number;
    name: string; // 路由名称
    path: string; // 路由地址
    title: string;
    redirect?: string; // 重定向到
    component: string; // 组件路径
    children?: MenuRoute[];
    meta: {
      link: boolean;
      title: string; // 菜单名称 ｜ 按钮名称
      isFull?: boolean; // 是否全屏
      isLink?: boolean; // 是否外链
      isHide?: boolean; // 是否隐藏
      isAffix?: boolean; // 是否吸附
      isKeepAlive?: boolean; // 是否缓存
      icon?: string; // 图标
      workflowId?: number;
      appCode: string;
      visible: string;
    };
  }
}
interface MenuItem {
  createBy: string | null;
  createTime: string;
  updateBy: string | null;
  updateTime: string | null;
  remark: string | null;
  menuId: number;
  menuName: string;
  parentName: string | null;
  parentId: number;
  orderNum: number;
  path: string;
  component: string | null;
  query: string;
  routeName: string;
  isFrame: string;
  isCache: string;
  menuType: string;
  visible: string;
  status: string;
  perms: string;
  icon: string;
  children?: MenuItem[]; // 树形结构需要
}
