<template>
  <div class="el-quarter-picker">
    <el-popover :visible="visible" :width="322" placement="bottom-start" trigger="click">
      <div>
        <div class="top-box">
          <el-icon @click="preYear">
            <DArrowLeft />
          </el-icon>
          <span>{{ currYear }} 年</span>
          <el-icon @click="nextYear">
            <DArrowRight />
          </el-icon>
        </div>
        <div class="content-box">
          <span :class="{ selected: i === currQuart }" @click="handleQuart(i)" v-for="i in quartList" :key="i.id">{{
            i.label
          }}</span>
        </div>
      </div>
      <template #reference>
        <el-input
          v-model="innerValue"
          @focus="onFocus"
          ref="inputRef"
          @blur="onBlur"
          :placeholder="placeholder"
          :prefix-icon="Calendar"
          :readonly="true"
        >
          <template #suffix>
            <el-icon @click="clear" v-show="clearVisible"><CircleClose /></el-icon>
          </template>
        </el-input>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, nextTick, onMounted } from "vue";
import { Calendar, DArrowRight, DArrowLeft, CircleClose } from "@element-plus/icons-vue";
const props = defineProps({
  placeholder: {
    type: String,
    default: "选择季度"
  },
  modelValue: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["change", "update:modelValue"]);

const innerValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

const visible = ref(false);
const currQuart = ref<any>({});
const currYear = ref<number>(new Date().getFullYear());
const quartList = ref<any>([
  { label: "Q1", id: 1, color: "var(--el-datepicker-text-color)" },
  { label: "Q2", id: 2, color: "var(--el-datepicker-text-color)" },
  { label: "Q3", id: 3, color: "var(--el-datepicker-text-color)" },
  { label: "Q4", id: 4, color: "var(--el-datepicker-text-color)" }
]);

function getCurrQuarter() {
  const now = new Date();
  const month = now.getMonth() + 1;
  let index = 0;
  if (month <= 3) {
    index = 0;
  } else if (month <= 6) {
    index = 1;
  } else if (month <= 9) {
    index = 2;
  } else {
    index = 3;
  }
  quartList.value[index].color = "var(--el-color-primary)";
}
getCurrQuarter();

const startDate = ref(["-01-01", "-04-01", "-07-01", "-10-01"]);
const endDate = ref(["-03-31", "-06-30", "-09-30", "-12-31"]);

const inputRef = ref<any>(null);

const nextYear = () => {
  currYear.value += 1;
};

const preYear = () => {
  currYear.value -= 1;
};

const handleQuart = (item: any) => {
  quartList.value.forEach((v: any) => {
    v.color = "var(--el-datepicker-text-color)";
    if (v.id === item.id) {
      v.color = "var(--el-color-primary)";
    }
  });
  currQuart.value = item;
  updateInnerValue();
  emitChange();
  visible.value = false;
  inputRef.value.blur();
};

const clearVisible = ref(false);
const clear = () => {
  innerValue.value = "";
  emit("change", { startDate: "", endDate: "" });
};

const onFocus = () => {
  visible.value = true;
};

const onBlur = () => {
  setTimeout(() => {
    visible.value = false;
  }, 10);
};

watch(
  () => innerValue.value,
  val => {
    initFromModelValue();
    nextTick(() => {
      clearVisible.value = !!val;
    });
  }
);

const initFromModelValue = () => {
  console.log("initFromModelValue", props.modelValue);
  if (props.modelValue) {
    const [year, q] = props.modelValue.split("-");
    currYear.value = parseInt(year);
    currQuart.value = quartList.value.find((item: any) => item.label === q);
  }
};

const updateInnerValue = () => {
  innerValue.value = `${currYear.value}-${currQuart.value.label}`;
};

const emitChange = () => {
  const emitDate = {
    startDate: `${currYear.value}${startDate.value[currQuart.value.id - 1]}`,
    endDate: `${currYear.value}${endDate.value[currQuart.value.id - 1]}`
  };
  emit("change", emitDate);
};

onMounted(() => {
  initFromModelValue();
});
</script>

<style scoped>
.el-quarter-picker {
  display: inline-block;
}
.top-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 12px;
  font-size: 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
.content-box {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100px;
  padding-top: 12px;
}
.content-box > span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148px;
  height: 40px;
  text-align: center;
  cursor: pointer;
}
.content-box > span.selected {
  color: var(--el-color-primary);
}
</style>
