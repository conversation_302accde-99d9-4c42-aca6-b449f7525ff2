<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'system:template:add'" type="primary" @click="openDictModal('新增')">{{ $t("新增") }}</el-button>
        <el-button v-auth="'system:template:edit'" type="primary" @click="openDictModal('编辑')">{{ $t("编辑") }}</el-button>
      </template>
      <template #template="{ row }">
        <el-link type="primary" target="_blank" :href="row.attachmentUrl"> {{ row.attachmentName }}</el-link>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button
          v-auth="'system:template:delete'"
          :disabled="!isSelected"
          type="danger"
          plain
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <EditModal ref="editModalRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
// import { getPostList, createPost, editPost, deletePost } from "@/api/modules/post";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import EditModal from "./components/EditModal.vue";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, nextTick, onMounted } from "vue";
// import { useAuthStore } from "@/stores/modules/auth";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
// import { useRoute, useRouter } from "vue-router";
import { Post } from "@/typings/post";
import { getTemplateList, createTemplate, templateListRemove } from "@/api/modules/template";
import { Template } from "@/typings/template";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
import { formatParams } from "@/utils/util";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// const router = useRouter();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const editModalRef = ref<InstanceType<typeof EditModal> | null>(null);
//

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Template.Item & ReqPage>({ pageNum: 1, pageSize: 10 } as Template.Item & ReqPage);

const { process_name } = useDict("process_name");

const pageButtons = ["template:edit", "template:delete"];
const supplierList = ref<Supplier.Item[]>([]);
// const auth = useAuthStore();
// const route = useRoute();
// 表格配置项
const columns = reactive<ColumnProps<Template.Item>[]>([
  { type: "selection", fixed: "left", width: 60 },
  {
    prop: "supplier",
    label: "供应商",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "process",
    label: "制程",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.process} placeholder={t("请选择")} clearable>
            {process_name.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "template",
    label: "模板"
  },
  { prop: "remark", label: "说明" },
  // {
  //   prop: "status",
  //   label: "状态",
  //   tag: false,
  //   enum: common_status,
  //   search: { el: "select" },
  //   fieldNames: { label: "label", value: "value" }
  // },
  {
    prop: "createdTime",
    label: "创建时间",
    render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  }
  // ...(visibleOperationCol(auth.authButtonList, pageButtons)
  //   ? [
  //       {
  //         prop: "operation",
  //         label: "操作",
  //         width: getOperationColWidth(auth.authButtonList, pageButtons),
  //         fixed: "right"
  //       }
  //     ]
  //   : [])
]);

const dataCallback = (data: ResPage<Template.Item>) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  const result = formatParams(initParam, params);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return getTemplateList(result);
};

const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(templateListRemove, { ids }, "删除所选信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openDictModal = (title: string, row: Partial<Post.Item> = {}) => {
  if (title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    form,
    // common_status: common_status.value,
    api: title === "新增" ? createTemplate : title === "编辑" ? createTemplate : void 0,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { status: "0" });
  editModalRef.value?.acceptParams(params);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};

onMounted(async () => {
  await getSupplierData();
});
</script>
