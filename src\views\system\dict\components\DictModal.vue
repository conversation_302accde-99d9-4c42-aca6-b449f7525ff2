<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="20">
          <el-col :span="12">
            <el-form-item :label="$t('字典名称')" prop="dictName">
              <el-input v-model="form.dictName" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('字典别名')" prop="dictType">
              <el-input v-model="form.dictType" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('说明')" prop="remark">
              <el-input v-model="form.remark" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('状态')" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{
                  $t(label)
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";
import { Dict } from "@/typings/dict";
import { useDict } from "@/hooks/useDict";
import useDictStore from "@/stores/modules/dict";
import { getDictDataList } from "@/api/modules/dict";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";

interface IState {
  title: string;
  isView: boolean;
  form: Partial<Dict.Detail>;
  common_status: Dict.IDataItem[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}
const { t } = useI18n();

const rules = {
  dictName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  dictType: [{ required: true, message: t("请填写"), trigger: "blur" }],
  label: [{ required: true, message: t("请填写"), trigger: "blur" }],
  value: [{ required: true, message: t("请填写"), trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  common_status: [],
  form: {}
});

const { form, title, isView, common_status } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!(form.value);
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
