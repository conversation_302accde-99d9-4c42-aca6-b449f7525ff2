import { ref } from "vue";
import { getLineList } from "@/api/modules/oba-smt/line";
import { Line } from "@/typings/line";

export const lineList = ref<Line.Item[]>([]);
export const getLinesList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getLineList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      lineList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
