export interface ProductionDataAnalysis {
  /**
   * 箱线图数据列表
   */
  candlestickDataList: CandlestickDataList[];
  /**
   * 箱线图标题
   */
  candlestickTitle: string;
  /**
   * 直方图统计量
   */
  histogramCalculate: HistogramCalculate;
  /**
   * 直方图列表
   */
  histogramDataList: number[];
  [property: string]: any;
}

export interface CandlestickDataList {
  date: string;
  list: number[];
  [property: string]: any;
}

/**
 * 直方图统计量
 */
export interface HistogramCalculate {
  AD: number;
  Ca: number;
  Cp: number;
  Cpk: number;
  CPL: number;
  Cpm: number;
  CPU: number;
  CV: number;
  LSL: number;
  Mean: number;
  n: number;
  normalDistribution: boolean;
  p: number;
  Pp: number;
  Ppk: number;
  PPL: number;
  PPU: number;
  SigmaTotal: number;
  SigmaWithin: number;
  Target: number;
  USL: number;
  [property: string]: any;
}
