<template>
  <div>
    <div class="card" style="margin-bottom: 10px">
      <div class="tab-wrap">
        <el-radio-group v-model="currentPage" size="large">
          <el-radio-button label="Measure Data" :value="1" />
          <el-radio-button label="VMI Data" :value="2" />
        </el-radio-group>
      </div>
    </div>
    <MeasureData v-if="currentPage === 1" />
    <VMIData v-if="currentPage === 2" />
  </div>
</template>

<script setup lang="tsx">
import MeasureData from "./MeasureData.vue";
import VMIData from "./VMIData.vue";
import { ref } from "vue";

const currentPage = ref(1);
</script>
<style scoped lang="scss">
.tab-wrap {
  text-align: center;
}
</style>
