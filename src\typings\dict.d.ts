export namespace Dict {
  export interface ITypeItem {
    dictType: string | null | number | undefined | LocationQueryValueRaw[];
    postId: number;
    dictId: number;
    id: number;
    name: string; // 字典名称
    type: string; // 字典类型
    remark: string; // 说明
    dictData: IDataItem[];
    dictDataList: IDataItem[];
    status: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    createdTime: string;
  }

  export interface IDataItem {
    id: number;
    label: string; // 键名
    value: string | boolean | number; // 键值
    dictType: string; // 字典类型
    tagType: string;
    sort: number;
    status: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    createdTime: string;
    remark: string;
  }

  export type Detail = {
    id?: number;
    label?: string; // 键名
    value?: string | boolean | number; // 键值
    dictType?: string; // 字典类型
    tagType?: string;
    sort?: number;
    status?: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    createdTime?: string;
    remark?: string;
    dictLabel?: string;
    dictValue?: string;
  };
}
