import { defineStore } from "pinia";
import { PostState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { Post } from "@/typings/post";
import { getPostAll } from "@/api/modules/post";

const usePostStore = defineStore({
  id: "post",
  state: (): PostState => ({
    post: [] as Post.Item[]
  }),
  getters: {},
  actions: {
    setPost(post: Post.Item[]) {
      this.post = post;
    },
    async getPost() {
      const { posts }: any = await getPostAll();
      const post = posts.map((post: any) => ({
        name: post.postName,
        id: post.postId
      }));
      this.setPost(post as any);
    }
  },
  persist: piniaPersistConfig("post")
});

export default usePostStore;
