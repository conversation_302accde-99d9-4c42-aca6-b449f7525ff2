export namespace Target {
  export interface Item {
    plant: string;
    statsType: number;
    statsTime: string;
    target: string;
  }

  export interface menuChild {
    parentName: string;
    name: string;
  }
  export interface menu {
    id: number;
    name: string;
    children: menuChild[];
  }

  export interface menuCreate {
    statsYear: string;
  }

  export interface target {
    plant: string;
    statsType: number;
    statsTime: string;
    target: string;
  }
}
