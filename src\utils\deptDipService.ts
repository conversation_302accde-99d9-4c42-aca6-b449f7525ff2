import { ref } from "vue";
import { getDeptList as DipDeptList } from "@/api/modules/ipqc-dip/dept_dip";
import { DeptDip } from "@/typings/ipqc-dip/dept_dip";

export const deptList = ref<DeptDip.Item[]>([]);
export const getDeptList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await DipDeptList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      deptList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
