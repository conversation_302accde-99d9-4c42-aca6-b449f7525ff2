<template>
  <el-select
    v-model="value"
    :multiple="props.multiple"
    :filterable="props.filterable"
    :remote="props.remote"
    :reserve-keyword="props.reserveKeyword"
    placeholder="请输入"
    :remote-method="remoteMethod"
    :loading="loading"
  >
    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
const props = defineProps({
  multiple: {
    type: Boolean,
    required: false
  },
  filterable: {
    type: Boolean,
    required: true
  },
  remote: {
    type: Boolean,
    required: true
  },
  reserveKeyword: {
    type: Boolean,
    required: true
  }
});

const options = ref<Supplier.Item[]>([]);
const value = ref<string[]>([]);
const loading = ref(false);

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    setTimeout(async () => {
      loading.value = false;
      const { data } = await getSupplierAll({ supplier: query });
      options.value = data || [];
    }, 200);
  } else {
    options.value = [];
  }
};
</script>
