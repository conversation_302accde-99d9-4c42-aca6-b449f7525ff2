<script setup lang="ts">
import Dialog from "@/components/Dialog/index.vue";
import ProTable from "@/components/ProTable/index.vue";
import useState from "@/hooks/useState";
import mittBus from "@/utils/mittBus";

const [visible, setVisible] = useState(false);

const [data, setData] = useState<any[]>([]);

const [columns, setColumns] = useState([]);

mittBus.on("openFullScreenTable", (payload: any) => {
  setData(payload.data);
  setColumns(payload.columns);
  setVisible(true);
});
</script>

<template>
  <Dialog v-model="visible" default-fullscreen>
    <ProTable class="is-fill-cell" :data="data" :columns="columns" :tool-button="false" scrollbar-always-on />
  </Dialog>
</template>
