<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="jobNum">
      <el-input v-model="loginForm.jobNum">
        <template #prefix>
          <el-icon class="mr-1 el-input__icon" :color="themeColor"><UserFilled /></el-icon>
          <span>{{ $t("工号") }}</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="email">
      <el-input v-model="loginForm.email">
        <template #prefix>
          <el-icon class="mr-1 el-input__icon" :color="themeColor"><UserFilled /></el-icon>
          <span>{{ $t("邮箱") }}</span>
        </template>
        <template #append>
          <el-button :disabled="isCountingDown" @click="sendVerificationCode">{{ $t(label) }}</el-button>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="code">
      <el-input v-model="loginForm.code">
        <template #prefix>
          <el-icon class="mr-1 el-input__icon" :color="themeColor"><UserFilled /></el-icon>
          <span>{{ $t("邮箱验证码") }}</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input v-model="loginForm.password" type="password" show-password autocomplete="new-password">
        <template #prefix>
          <el-icon class="mr-1 el-input__icon" :color="themeColor">
            <svg
              t="1700548311789"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="5843"
              width="16"
              height="16"
            >
              <path
                d="M893.155556 366.933333h-108.088889v-73.955555C785.066667 119.466667 679.822222 0 517.688889 0c-199.111111 0-278.755556 119.466667-278.755556 292.977778v73.955555H128C82.488889 366.933333 56.888889 398.222222 56.888889 440.888889v517.688889C56.888889 1001.244444 82.488889 1024 128 1024h765.155556c45.511111 0 73.955556-31.288889 73.955555-73.955556v-512c0-42.666667-28.444444-71.111111-73.955555-71.111111zM566.044444 694.044444l5.688889 85.333334c0 31.288889-28.444444 45.511111-59.733333 45.511111s-51.2-17.066667-51.2-45.511111l-5.688889-85.333334c-17.066667-14.222222-28.444444-36.977778-28.444444-62.577777 0-45.511111 39.822222-82.488889 85.333333-82.488889s85.333333 39.822222 85.333333 85.333333c2.844444 25.6-14.222222 45.511111-31.288889 59.733333z m110.933334-327.111111H349.866667V284.444444c0-36.977778 25.6-190.577778 176.355555-190.577777 96.711111 0 153.6 85.333333 153.6 190.577777v82.488889z"
                :fill="themeColor"
                p-id="5844"
              />
            </svg>
          </el-icon>
          <span>{{ $t("密码") }}</span>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
  <div class="login-btn">
    <el-button :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login(loginFormRef)">
      {{ $t("确认") }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
// import { confirmPassword, sendEmailCode } from "@/api/modules/sys-retrieve-password";
// import { useCountdownTimer } from "@/hooks/useCountdownTimer";
import { ref, reactive, onMounted, computed } from "vue";
import { UserFilled } from "@element-plus/icons-vue";
import useGlobalStore from "@/stores/modules/global";
import type { Action, ElForm } from "element-plus";
import { ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import md5 from "md5";
import { isEmpty } from "@/utils/is";

interface IProps {
  forgetPassword: boolean;
}

interface IEmit {
  (e: "update:forgetPassword", value: boolean): void;
}

defineProps<IProps>();
const { t } = useI18n();
const emit = defineEmits<IEmit>();

const globalStore = useGlobalStore();

const themeColor = computed(() => globalStore.primary);

const countdownSeconds = 60;

const { isCountingDown, remainingSeconds, startCountdown, stopCountdown } = useCountdownTimer(countdownSeconds);

const label = computed(() => {
  return isCountingDown.value ? `${remainingSeconds.value}s后重新发送` : "获取验证码";
});

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  jobNum: [{ required: true, message: "请填写员工工号" }],
  code: [{ required: true, message: "请填写邮箱验证码" }],
  email: [
    { required: true, message: "请填写邮箱", trigger: "blur" },
    {
      type: "email",
      message: "请填写正确邮箱",
      trigger: ["blur", "change"]
    }
  ],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const loading = ref(false);

const loginForm = reactive({
  jobNum: "",
  email: "",
  code: "",
  password: ""
});

const sendVerificationCode = async () => {
  try {
    await loginFormRef?.value?.validateField("jobNum");
    await loginFormRef?.value?.validateField("email");
    // await sendEmailCode(loginForm);
    startCountdown();
  } catch (e) {}
};

// login
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // const { success } = await confirmPassword({ ...loginForm, password: md5(loginForm.password) });
      // if (success) {
      //   ElMessageBox.alert(t("修改密码成功"), t("成功"), {
      //     type: "success",
      //     confirmButtonText: t("前往登录"),
      //     callback: (action: Action) => {
      //       emit("update:forgetPassword", false);
      //     }
      //   });
      //   console.log("修改密码成功");
      // }
    } finally {
      loading.value = false;
    }
  });
};

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
