<script setup lang="ts" name="QuickEntry">
import { Plus } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { computed, ref, watchEffect } from "vue";
import QuickMenuModal from "./QuickMenuModal.vue";
import { delQuickEntry, getQuickEntry } from "@/api/modules/user";
import { isEmpty } from "@/utils/is";
import { App } from "@/typings/app";
import { Home } from "@/typings/home";

interface IProps {
  admin: App.Item;
}

const props = defineProps<IProps>();

const admin = computed(() => props.admin);

const router = useRouter();

const quickMenuModalRef = ref<InstanceType<typeof QuickMenuModal>>();

const quickEntry = ref<Home.IQuickMenu[]>([]);

const menuIds = computed(() => quickEntry.value.map(item => item.entryId));

const scrollItemGap = computed(() => 10);

const scrollItemCss = computed(
  () =>
    `relative px-6 py-4 bg-[#009dff] hover:bg-[#009dffcc] flex flex-col flex-shrink-0 items-center justify-center rounded text-white cursor-pointer transition-all`
);

const _getQuickEntry = async () => {
  if (isEmpty(admin.value)) return;
  const { data } = await getQuickEntry({ appCode: admin.value.code });
  quickEntry.value = data;
};

const handleAdd = () => {
  console.log("menuIds", menuIds);
  quickMenuModalRef.value?.setVisible(true);
};

const handleClick = (path: string) => {
  router.push({ path });
};

const handleDel = async (id: number) => {
  await delQuickEntry(id);
  await _getQuickEntry();
};

watchEffect(async () => {
  admin.value;
  await _getQuickEntry();
});
</script>

<template>
  <div class="flex flex-col flex-shrink-0 gap-4 card">
    <div class="text-[20px] font-bold">
      {{ $t("快捷入口") }}
      <el-button :icon="Plus" size="small" text circle @click="handleAdd" />
    </div>
    <transition-group :style="{ gap: scrollItemGap + 'px' }" name="el-fade-in-linear" tag="div" class="flex flex-wrap">
      <template v-for="{ id, title, path } of quickEntry" :key="id">
        <div v-if="!isEmpty(path)" :class="scrollItemCss" class="group" @click="handleClick(path)">
          <div>{{ $t(title) }}</div>
          <div class="absolute transition-all opacity-0 group-hover:opacity-100 top-1 right-1" @click.stop="handleDel(id)">
            <el-icon><CircleClose /></el-icon>
          </div>
        </div>
      </template>
    </transition-group>
  </div>
  <QuickMenuModal ref="quickMenuModalRef" :menu-ids="menuIds" :admin="admin" @submit-success="_getQuickEntry" />
</template>
