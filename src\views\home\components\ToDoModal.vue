<template>
  <el-dialog v-model="visible" width="40%" title="客户">
    <el-form ref="formRef" label-width="110px" label-suffix=" :" :rules="rules" :model="form">
      <el-row>
        <el-col :span="18">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="人员" prop="userId">
            <el-select v-model="form.userId" :placeholder="t('请选择')">
              <el-option v-for="{ name, id } of staffList" :key="id" :label="name" :value="id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="菜单" prop="menuId">
            <el-select v-model="form.menuId" :placeholder="t('请选择')" @change="handleMenuChange">
              <el-option v-for="{ title: menuTitle, id } of menuList" :key="id" :label="menuTitle" :value="id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="ToDoModal">
import { ref, computed, watchEffect, onMounted } from "vue";
import { createToDo, editToDo, getToDoDetail } from "@/api/modules/home";
import { ElMessage, FormInstance } from "element-plus";
import { getMenuList } from "@/api/modules/menu";
import { Staff } from "@/typings/staff";
import { Home } from "@/typings/home";
import { Menu } from "@/typings/menu";
import { isEmpty } from "@/utils/is";
import { App } from "@/typings/app";

interface IEmits {
  (e: "submitSuccess"): void;
}

interface IProps {
  todoId?: number;
  staffList: Staff.Item[];
  admin: App.Item;
}

const props = defineProps<IProps>();

const emits = defineEmits<IEmits>();

const rules = {};

const menuList = ref<Menu.Item[]>([]);

const todoId = computed(() => props.todoId);

const staffList = computed(() => props.staffList);

const admin = computed(() => props.admin);

const title = computed(() => (todoId.value ? "编辑" : "新增"));

const formRef = ref<FormInstance>();

const visible = ref(false);

const form = ref<Home.IToDo>({} as Home.IToDo);

const handleMenuChange = (e: number) => {
  if (isEmpty(e)) return;
  form.value.path = menuList.value.find(({ id }) => id === e)?.path || "";
};

const _getMenuList = async () => {
  const { data } = await getMenuList();
  menuList.value = data;
};

const resetForm = () => {
  form.value = { appCode: admin.value.code, url: admin.value.url } as Home.IToDo;
};

const getToDo = async () => {
  if (isEmpty(todoId.value)) return resetForm();
  const { data } = await getToDoDetail(todoId.value!);
  form.value = data;
};

const setVisible = (value: boolean) => {
  visible.value = value;
};

const handleSubmit = async () => {
  try {
    formRef.value!.validate(async valid => {
      if (!valid) return;
      try {
        const {} = isEmpty(form.value.id) ? await createToDo(form.value) : await editToDo(form.value);
        ElMessage.success({ message: `${title.value}待办成功！` });
        setVisible(false);
        emits("submitSuccess");
        resetForm();
      } catch (error) {
        console.log(error);
      }
    });
  } catch (error) {}
};

watchEffect(() => {
  todoId.value;
  getToDo();
});

onMounted(() => {
  _getMenuList();
});

defineExpose({
  setVisible
});
</script>
