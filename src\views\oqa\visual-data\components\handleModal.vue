<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form ref="formRef" label-width="150px" label-suffix=" :" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row>
        <el-col :span="12">
          <el-form-item label="Date">
            <el-date-picker
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              v-model="form.date"
              type="date"
              placeholder="Pick a day"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Supplier">
            <el-select v-model="form.supplier" placeholder="Please Select" clearable>
              <el-option v-for="item in supplierList" :key="item.id" :label="item.supplier" :value="item.supplier" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Product">
            <el-input v-model="form.product" placeholder="Please enter" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Model">
            <el-input v-model="form.model" placeholder="Please enter" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="Model">
            <el-input v-model="form.model" placeholder="Please enter" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="DO">
            <el-input v-model="form.doNo" placeholder="Please enter" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Lot No">
            <el-input v-model="form.lotNo" placeholder="Please enter" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="WeekCode">
            <el-input v-model="form.weekCode" placeholder="Please enter" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label=" Defect Category">
            <el-input v-model="form.defectCategory" placeholder="Please enter" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Defect Qty">
            <el-input v-model="form.defectQty" placeholder="Please enter" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="RoleModal">
import { ExceptionManageForm } from "@/typings/exception-manage";
import { getStaffList } from "@/api/modules/staff";
import { ElMessage, FormInstance, ElTreeSelect } from "element-plus";
import UploadFiles from "@/components/Upload/UploadFiles.vue";
import { ref, reactive, toRefs } from "vue";
import { isEmpty } from "@/utils/is";
import { useI18n } from "vue-i18n";
import { useDict } from "@/hooks/useDict";
import { getUserList } from "@/api/modules/user";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
// const { product_names, report_exception_type, report_node_type_8d, common_status, report_source } = useDict(
//   "product_names",
//   "report_exception_type",
//   "report_node_type_8d",
//   "common_status",
//   "report_source"
// );
interface IState {
  title: string;
  isView: boolean;
  form: Partial<ExceptionManageForm>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const { t } = useI18n();

const rules = {
  // title: [{ required: true, message: "请选择", trigger: "blur" }],
  // exceptionType: [{ required: true, message: "请选择", trigger: "blur" }],
  // supplierName: [{ required: true, message: "请填写", trigger: "blur" }],
  // product: [{ required: true, message: "请选择", trigger: "blur" }],
  // responsibleBy: [{ required: true, message: "请选择", trigger: "blur" }]
};

// const treeSelectRef = ref<InstanceType<typeof ElTreeSelect> | null>();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

// const menuList = ref<Menu.Item[]>([]);

const { form, title, isView } = toRefs(state);
const supplierList = ref<Supplier.Item[]>([]);
const setVisible = (val: boolean) => {
  visible.value = val;
};
const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
  await getSupplierData();
};
// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({
        ...form.value
      });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style>
.tree-border {
  width: 100%;
  margin-top: 5px;
  background: #ffffff none;
  border: 1px solid #e5e6e7;
  border-radius: 4px;
}
</style>
