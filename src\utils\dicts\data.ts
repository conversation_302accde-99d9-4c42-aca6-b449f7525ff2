import { StatusTypes } from "./type";

/**
 * 操作状态
 */
export const operationStatus: StatusTypes[] = [
  {
    label: "停用",
    status: 1,
    value: 1,
    tagType: "success"
  },
  {
    label: "正常",
    status: 0,
    value: 0,
    tagType: "danger"
  }
];
export const common_yes_no: StatusTypes[] = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
];
export const common_status: StatusTypes[] = [
  {
    label: "停用",
    status: 1,
    value: 1,
    tagType: "success"
  },
  {
    label: "正常",
    status: 0,
    value: 0,
    tagType: "danger"
  }
];

export const job_grade: StatusTypes[] = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
];
export const audit_status: StatusTypes[] = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
];
export const languages = [
  { label: "中文", value: "zh-CN" },
  { label: "English", value: "en-US" }
];
export const languageCode = {
  zh: "zh-CN",
  en: "en-US"
};

//   [
//   { label: "zh", value: "zh-CN" },
//   { label: "en", value: "en-US" }
// ];
