import { ReqPage, ResPage, ResultData } from "@/api/interface/index";
import { Post } from "@/typings/post";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Supplier } from "@/typings/supplier";

// 列表
export const getSupplierList = (params?: ReqPage) => {
  return http.post<ResPage<Supplier.Item>>(`${API_PREFIX}/supplier/list`, params);
};

export const getSupplierAll = (params: any) => {
  return http.get<any>(`${API_PREFIX}/supplier/listAll`, params, { loading: false });
};
// 详情
export const getPostDetail = (id: number) => {
  return http.get<Supplier.Item>(`${API_PREFIX}/${id}`);
};

// 新增
export const createSupplier = (data: Supplier.Item) => {
  return http.post(`${API_PREFIX}/supplier/save`, data);
};

// 修改
export const editPost = (data: Supplier.Item) => {
  return http.put(`${API_PREFIX}/system/post`, data);
};

// 删除
export const deleteSupplier = (ids: any) => {
  return http.post(`${API_PREFIX}/supplier/del`, ids);
};

// 导出
export const exportPost = (ids?: number[]) => {
  return http.post(`${API_PREFIX}/export`, { ids });
};
