<template>
  <el-dialog v-model="dialogVisible" :title="$t('修改密码')" width="500px" draggable>
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item :label="$t('旧密码')" prop="oldPassword">
        <el-input v-model="form.oldPassword" :placeholder="$t('请输入旧密码')" type="password" />
      </el-form-item>
      <el-form-item :label="$t('新密码')" prop="password">
        <el-input v-model="form.password" type="password" :placeholder="$t('请输入新密码')" />
      </el-form-item>
      <el-form-item :label="$t('确认密码')" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" :placeholder="$t('请输入新密码')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t("取消") }}</el-button>
        <el-button type="primary" @click="handleSubmit">{{ $t("确认") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { editPassword } from "@/api/modules/staff";
import useUserStore from "@/stores/modules/user";

import { ElMessage, FormInstance } from "element-plus";
import md5 from "md5";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const dialogVisible = ref(false);

const userStore = useUserStore();

const equalToPassword = (rule: any, value: string, callback: any) => {
  if (form.value.password !== value) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};
const equalToOldPassword = (rule: any, value: string, callback: any) => {
  if (form.value.oldPassword === value) {
    callback(new Error("新密码不可与原密码相同"));
  } else {
    callback();
  }
};

const rules = {
  oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  password: [
    { required: true, message: "新密码不能为空", trigger: "blur" },
    { required: true, validator: equalToOldPassword, trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "确认密码不能为空", trigger: "blur" },
    { required: true, validator: equalToPassword, trigger: "blur" }
  ]
};

const form = ref<{ password: string; confirmPassword: string; oldPassword: string }>({
  password: "",
  confirmPassword: "",
  oldPassword: ""
});

const formRef = ref<FormInstance>();

const openDialog = () => {
  form.value = { oldPassword: "", password: "", confirmPassword: "" };
  dialogVisible.value = true;
};

const handleSubmit = async () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { data } = await editPassword({ password: md5(form.value.password!), oldPassword: md5(form.value.oldPassword) });
      userStore.setUserInfo(data);
      ElMessage.success({ message: t(`修改密码成功！`) });
      dialogVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({ openDialog });
</script>
