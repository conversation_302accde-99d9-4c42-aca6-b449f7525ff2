<template>
  <div style="width: 100%">
    <el-select
      v-model="selectedLabel"
      filterable
      remote
      reserve-keyword
      :placeholder="$t('请搜索选择')"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="handleChange"
      clearable
    >
      <el-option v-for="item in options" :key="item.id" :label="getLabel(item)" :value="item.id">
        {{ getLabel(item) }}
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts" name="RemoteSearchJobNum">
import { ref, watch, onMounted } from "vue";
import { debounce } from "lodash";
import { getStaffList } from "@/api/modules/staff";
import { Staff } from "@/typings/staff";
import { isEmpty } from "@/utils/is";

interface Props {
  resetSignal?: Number;
  modelValue: string;
  jobNum?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  jobNum: ""
});

const emit = defineEmits(["update:modelValue", "extra"]);
const selectedLabel = ref<string | null>(null);
const options = ref<Staff.Extra[]>([]);
const loading = ref(false);

const getLabel = (item: Staff.Extra) => {
  return !isEmpty(item.jobNum) ? `[${item.jobNum}] ${item.name}` : item.name;
};

const search = async (query: string) => {
  if (query === "") {
    return;
  }
  if (query !== "") {
    loading.value = true;
    try {
      const {
        data: { list }
      } = await getStaffList({ name: query, pageSize: 20, pageNum: 1 } as any);
      options.value = list.map((item: any) => ({
        id: String(item.id),
        name: item.name,
        jobNum: String(item.jobNum),
        postId: item.postId,
        deptId: item.deptId,
        email: item.email
      }));
    } catch (error) {
      console.error("Error fetching options:", error);
    } finally {
      loading.value = false;
    }
  } else {
    options.value = [];
  }
};

const remoteMethod = debounce((query: string) => {
  search(query);
}, 500);

const handleChange = (id: string) => {
  if (isEmpty(id)) {
    emit("update:modelValue", "");
    emit("extra", {
      id: "",
      name: "",
      jobNum: "",
      postId: "",
      deptId: "",
      email: ""
    });
  } else {
    const selectedItem = options.value.find(item => item.id === id);
    if (selectedItem) {
      emit("update:modelValue", selectedItem.jobNum);
      emit("extra", selectedItem);
    }
  }
};

// 监听外部传入的值变化
watch(
  [() => props.modelValue, () => props.jobNum],
  ([name, jobNum]) => {
    if (name) {
      // 如果有初始值，添加到选项中
      const newOption = {
        id: `temp_${Date.now()}`, // 临时ID
        name,
        jobNum: jobNum || "",
        postId: "",
        deptId: "",
        email: ""
      } as any;
      options.value = [newOption];
      selectedLabel.value = newOption.id;
    } else {
      selectedLabel.value = null;
    }
  },
  { immediate: true }
);

watch(
  () => props.resetSignal,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      selectedLabel.value = null;
    }
  }
);
</script>
