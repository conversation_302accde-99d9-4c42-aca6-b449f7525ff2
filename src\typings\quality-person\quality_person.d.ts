export namespace QualityPerson {
  export interface Item {
    plant: string;
    staffName: string;
    dept: string;
    deptId: number;
    post: string;
    workNo: number;
    gender: string;
    email: string;
    qualityType: string;
    certifiedDate: string;
    validDate: string;
    certificateFile: string;
    certificateFileUrl: string;
    certificateStatus: number;
    certificateNo: string;
    entryDate: string;
    auditorLevel: string;
    remarks: string;
    trainingDate: string;
    course: string;
    institutions: string;
    staffStatus: number;
    superiorManager: string;
    deleted: number;
    superiorEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  interface IQueryParams {
    startDate: string;
    endDate: string;
  }
}
