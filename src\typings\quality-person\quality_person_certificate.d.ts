export namespace QualityPersonCertificate {
  export interface Item {
    plant: string;
    qualityType: string;
    certifiedDate: string;
    validDate: string;
    certificateFile: string;
    certificateFileUrl: string;
    certificateStatus: number;
    certificateNo: string;
    deleted: number;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  interface IQueryParams {
    startDate: string;
    endDate: string;
  }
}
