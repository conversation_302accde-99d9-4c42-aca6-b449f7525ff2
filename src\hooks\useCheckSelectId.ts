import { ProTableInstance } from "@/components/ProTable/interface";
import { getMessage } from "@/utils";
import { isEmpty } from "@/utils/is";
import { ComponentInternalInstance, computed, getCurrentInstance } from "vue";

export const useCheckSelectId = () => {
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const selectedList = computed(() => (proxy?.$refs?.proTable as ProTableInstance).selectedList);

  const selectedIds = computed(() => (proxy?.$refs?.proTable as ProTableInstance).selectedListIds);
  const tableData = computed(() => (proxy?.$refs?.proTable as ProTableInstance).tableData);
  const currentSelectedId = computed(() => {
    if (isEmpty(selectedIds.value)) return 0;
    return selectedIds.value[0];
  });

  const currentRow = computed(() => {
    if (isEmpty(selectedList.value)) return {};
    return tableData.value.find(item => item.id === currentSelectedId.value) || selectedList.value[0] || {};
  });

  function check() {
    if (selectedList.value.length === 0) {
      throw new Error(getMessage("请选择一条数据"));
    }
    if (selectedList.value.length > 1) {
      throw new Error(getMessage("当前操作只能选择一条数据"));
    }
  }

  return {
    check,
    currentRow,
    currentSelectedId
  };
};
