import { Dept } from "./dept";
export namespace Staff {
  export interface Item {
    userId: number;
    roles: any[];
    posts: any[];
    id: number;
    name: string;
    avatar: string;
    jobNum: number; // 工号
    tel: string; // 手机号
    password?: string; // 密码;
    email: string; // 邮箱
    rank?: string; // 职级 目前先是字符串 可能需要维护字典
    postId: any[]; //id
    roleId: any[];
    deptId: number;
    deptParent: number;
    company: number; // 公司/ 工厂
    leader: number; // 直属领导
    status: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    auditStatus: string;
    createdTime: string;
    indirectLeader: number;
    deptName?: string;
    postName?: string;
    leaderName?: string;
    leaderEmail?: string;
    leaderJobNum?: number;
    level?: string;
    confirmPassword?: string;
  }

  export interface Extra {
    id: string;
    name: string;
    jobNum: string;
    postId: number;
    deptId: number;
    email: string;
  }

  export interface qualityPersonItem {
    id: number;
    plant: string;
    staffName: string;
    dept: string;
    deptId: number;
    post: string;
    workNo: number;
    gender: string;
    email: string;
    qualityType: string;
    certifiedDate: string;
    validDate: string;
    certificateFile: string;
    certificateFileUrl: string;
    certificateStatus: number;
    certificateNo: string;
    entryDate: string;
    auditorLevel: string;
    remarks: string;
    trainingDate: string;
    course: string;
    institutions: string;
    staffStatus: number;
    superiorManager: string;
    deleted: number;
    superiorEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }

  export interface AuditItem extends Item {}

  export interface staffForm {
    posts: any[];
    roles: any[];
    postId: any[];
    roleId: any[];
    deptId: number;
    userName: number;
    deptName: string;
    phonenumber: number;
    email: any;
    loginName: any;
    password: string;
    sex: string;
    role: string;
    remark: string;
    status: string;
    roleIds: string;
    postIds: any;
    userId: string;
  }
}
