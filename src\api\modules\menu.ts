import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { MenuItem, Menu } from "@/typings/menu";
import { buildMenuTree } from "@/utils";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";
const baseUrl = "/admin-api";

export const getMenuList = (params?: ReqPage) => {
  return http.get<{ data: MenuItem[] }>(`${API_PREFIX}/system/menu/list`, params);
};

export const getMenuTree = (params?: ReqPage) => {
  return http.get<{ data: MenuItem[] }>(`${API_PREFIX}/system/menu/treeselect`, params);
};

export const getMenuDetail = (menuId: number) => {
  return http.get<Menu.Item>(`${API_PREFIX}/${menuId}`);
};

export const createMenu = (data: Menu.Item) => {
  return http.post(`${API_PREFIX}/system/menu`, data);
};

export const editMenu = (data: Menu.Item) => {
  return http.put(`${API_PREFIX}/system/menu`, data);
};

export const deleteMenu = (data: any) => {
  return http.delete(`${API_PREFIX}/system/menu/${data.id}`);
};
