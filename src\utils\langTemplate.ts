import { ref } from "vue";
import { getCustomerSatTmplList } from "@/api/modules/customer-satisfaction/customer_sat_tmpl";
import { CustomerSatTmpl } from "@/typings/customer-satisfaction/customer_sat_tmpl";

export const templateList = ref<CustomerSatTmpl.Item[]>([]);
export const getLangTemplateList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getCustomerSatTmplList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      templateList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
