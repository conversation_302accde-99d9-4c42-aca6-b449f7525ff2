import { ReqPage, ResPage } from "@/api/interface/index";
import { Post } from "@/typings/post";
import http from "@/api";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_post`;

// 列表
export const getPostList = (params?: ReqPage) => {
  return http.get<ResPage<Post.Item>>(`${API_PREFIX}/system/post/list`, params);
};

export const getPostAll = () => {
  return http.get<any>(`${API_PREFIX}/system/user/`, {}, { loading: false });
};
// 详情
export const getPostDetail = (id: number) => {
  return http.get<Post.Item>(`${API_PREFIX}/${id}`);
};

// 新增
export const createPost = (data: Post.Item) => {
  return http.post(`${API_PREFIX}/system/post`, data);
};

// 修改
export const editPost = (data: Post.Item) => {
  return http.put(`${API_PREFIX}/system/post`, data);
};

// 删除
export const deletePost = (ids: string) => {
  return http.delete(`${API_PREFIX}/system/post/` + ids);
};

// 导出
export const exportPost = (ids?: number[]) => {
  return http.post(`${baseUrl}/export`, { ids });
};
