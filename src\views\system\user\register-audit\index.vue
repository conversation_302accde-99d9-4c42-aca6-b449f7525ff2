<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" :data-callback="dataCallback">
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button
          v-if="row.status === '0'"
          v-auth="'staff-register-audit:pass'"
          type="primary"
          link
          @click="pass(row.id, 'staff-register-audit:pass')"
        >
          通过
        </el-button>
        <el-button
          v-if="row.status === '0'"
          v-auth="'staff-register-audit:reject'"
          type="danger"
          link
          @click="reject(row.id, 'staff-register-audit:reject')"
        >
          拒绝
        </el-button>
        <el-button v-if="isHasWorkflow" type="primary" link @click="viewWorkFlow(row.id)">查看工作流</el-button>
      </template>

      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button type="danger" plain :disabled="!isSelected" @click="batchDelete(selectedListIds as number[])">
          批量删除
        </el-button>
        <el-button
          type="primary"
          plain
          :disabled="!isSelected"
          @click="pass(selectedListIds as number[],  'staff-register-audit:pass')"
        >
          批量通过
        </el-button>
        <el-button
          type="danger"
          plain
          :disabled="!isSelected"
          @click="reject(selectedListIds as number[], 'staff-register-audit:reject')"
        >
          批量拒绝
        </el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="StaffAuditTable">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getStaffList } from "@/api/modules/staff";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { findTopParent, handleTree } from "@/utils";
import { getRoleList } from "@/api/modules/role";
import { getDeptList } from "@/api/modules/dept";
import { getPostList } from "@/api/modules/post";
import { ref, reactive, onMounted, computed } from "vue";
import { useDict } from "@/hooks/useDict";
import { Staff } from "@/typings/staff";
import { Post } from "@/typings/post";
import { Dept } from "@/typings/dept";
import { Role } from "@/typings/role";
import { isArray, isEmpty } from "@/utils/is";
import { auditStaff, deleteStaffAudit, getStaffAuditList } from "@/api/modules/staff_audit";
import { useRouter, useRoute } from "vue-router";
import { useDateFormat } from "@vueuse/core";
import { useWorkFlow } from "@/hooks/useWorkFlow";
import useGlobalStore from "@/stores/modules/global";
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { common_status, job_grade, audit_status } = useDict("common_status", "job_grade", "audit_status");
const router = useRouter();
const route = useRoute();
const name = computed(() => route.query.name as string);
const postList = ref<Post.Item[]>([]);

const globalStore = useGlobalStore();

onMounted(() => {
  console.log("route", router.currentRoute.value);
});

const { menuId, workflowId, isHasWorkflow, workInfo, viewWorkFlow } = useWorkFlow();

const initParam = reactive<{ name: string; deptId?: string }>({ name: name.value });

const flatDeptList = ref<Dept.Item[]>([]);

const treeDeptList = ref<Dept.Item[]>([]);

const roleList = ref<Role.Item[]>([]);
const pass = async (id: number | number[], func: string) => {
  const ids = isArray(id) ? id : [id];
  globalStore.setGlobalState("btnAction", func);
  await useHandleData(auditStaff, { ids, status: "1" }, `通过`);
  proTable.value?.getTableList();
};
const reject = async (id: number | number[], func: string) => {
  const ids = isArray(id) ? id : [id];
  globalStore.setGlobalState("btnAction", func);

  try {
    await ElMessageBox.confirm("确认拒绝?", "提示", { type: "warning" });
    const { value } = await ElMessageBox.prompt("请输入拒绝原因", "", {
      confirmButtonText: "确认",
      cancelButtonText: "取消"
    });
    await auditStaff({ ids, status: "2", reason: value });
    proTable.value?.getTableList();
  } catch (error) {}
};
const getAllPostList = async () => {
  const {
    data: { list }
  } = await getPostList();
  postList.value = list;
};

const getAllDeptList = async () => {
  const { data } = await getDeptList();
  treeDeptList.value = handleTree(data);
  flatDeptList.value = data;
  return { data: handleTree(data) };
};

const getAllRoleList = async () => {
  const {
    data: { list }
  } = await getRoleList();
  roleList.value = list;
};

const getTableList = (params: any) => {
  return getStaffAuditList(params);
};

const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};
// 表格配置项
const columns = reactive<ColumnProps<Staff.Item>[]>([
  { type: "index", label: "序号", width: 80 },
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "name",
    label: "姓名",
    width: 120,
    search: {
      el: "input",
      render: () => {
        return <el-input v-model={initParam.name} placeholder="请输入姓名" />;
      }
    }
  },
  {
    prop: "jobNum",
    label: "工号",
    width: 120
  },
  { prop: "postId", label: "职位", width: 150, enum: postList, fieldNames: { label: "name", value: "id" } },
  { prop: "rank", label: "职级", width: 150, enum: job_grade },
  { prop: "tel", label: "手机号码", width: 150 },
  { prop: "email", label: "邮箱", width: 150 },
  {
    prop: "deptId",
    label: "部门",
    width: 150,
    search: {
      render: () => {
        return (
          <el-tree-select
            v-model={initParam.deptId}
            data={treeDeptList.value}
            props={{ value: "id", label: "name", children: "children" }}
            value-key="id"
            placeholder="请选择部门"
            check-strictly
          />
        );
      }
    },
    enum: treeDeptList,
    fieldNames: { label: "name", value: "id" }
  },
  {
    prop: "deptParent",
    label: "上级部门",
    width: 120,
    render: ({ row }) => {
      const currentDept = flatDeptList.value.find(item => item.id === row.deptId);
      if (!isEmpty(currentDept) && !isEmpty(currentDept?.parentId)) {
        const parentDept = flatDeptList.value.find(item => item.id === currentDept!.parentId);
        return <span>{parentDept?.name ?? "--"}</span>;
      }
      return <span>--</span>;
    }
  },
  {
    prop: "company",
    label: "公司/工厂",
    width: 120,
    search: { el: "input" },
    render: ({ row }) => {
      if (!isEmpty(row.deptId)) {
        return <span>{findTopParent(flatDeptList.value, row.deptId)?.name}</span>;
      }
      return <span>---</span>;
    }
  },
  { prop: "leader", label: "直属领导", width: 120 },
  {
    prop: "roleId",
    label: "角色名称",
    width: 200,
    enum: roleList,
    fieldNames: { label: "name", value: "id" }
  },
  {
    prop: "status",
    label: "状态",
    width: 120,
    tag: true,
    enum: audit_status,
    search: { el: "select" }
  },
  {
    prop: "createdTime",
    label: "创建时间",
    width: 180,
    render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]);
const batchDelete = async (id: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  await useHandleData(deleteStaffAudit, { ids }, `删除`);
  proTable.value?.getTableList();
};

// 批量添加用户
onMounted(() => {
  getAllPostList();
  getAllRoleList();
  getAllDeptList();
});
</script>
