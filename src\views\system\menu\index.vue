<template>
  <div class="table-box">
    <ProTable
      :key="compKey"
      :indent="20"
      :columns="columns"
      :request-api="getTableList"
      :request-auto="true"
      :pagination="false"
      :data-callback="dataCallback"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
      :default-expand-all="isExpandAll"
      ref="proTable"
      row-key="menuId"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" @click="toggleExpandAll">{{ $t("全部展开/折叠") }}</el-button>
        <el-button v-auth="'system:menu:add'" type="primary" @click="openMenuModal('新增')">{{ $t("新增") }}</el-button>
      </template>
      <template #icon="{ row }">
        <el-icon :size="18" v-if="row.icon && row.icon !== '#'">
          <component :is="row.icon"></component>
        </el-icon>
      </template>
      <!--      <template #status="{ row }">-->
      <!--        {{ row.status === 1 ? t('停用') :  }}-->
      <!--      </template>-->
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button v-auth="'system:menu:edit'" type="primary" link @click="openMenuModal('修改', row)">{{
          $t("修改")
        }}</el-button>
        <el-button v-auth="'system:menu:add'" type="primary" link @click="openMenuModal('新增', { parentId: row.menuId })">
          {{ $t("新增") }}
        </el-button>
        <el-button v-auth="'system:menu:remove'" type="danger" link @click="delMenu(row.menuId)">{{ $t("删除") }}</el-button>
      </template>
    </ProTable>
    <MenuModal ref="deptModalRef" />
  </div>
</template>

<script setup lang="tsx" name="MenuTable">
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { createMenu, deleteMenu, editMenu, getMenuList } from "@/api/modules/menu";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import MenuModal from "./components/MenuModal.vue";
import { isArray, isEmpty } from "@/utils/is";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
import { Menu, MenuItem } from "@/typings/menu";
import { handleTree, getOperationColWidth, visibleOperationCol, buildMenuTree } from "@/utils";
import { reactive, ref, onMounted } from "vue";
import { App } from "@/typings/app";
import { getAppList } from "@/api/modules/app";
import { useAuthStore } from "@/stores/modules/auth";
import { useI18n } from "vue-i18n";
// import { operationStatus } from "@/utils/dicts";
// ProTable 实例
const { common_status } = useDict("common_status");
const proTable = ref<ProTableInstance>();

const deptModalRef = ref<InstanceType<typeof MenuModal> | null>(null);

const isExpandAll = ref(false);

const refreshTable = ref(true);

const compKey = ref(0);
const { t } = useI18n();
// const { common_status } = useDict("common_status");
// const common_status = {};
const appList = ref<App.Item[]>([]);

const pageButtons = ["system:menu:edit", "system:menu:add", "system:menu:remove"];

const auth = useAuthStore();
// 表格配置项
const columns = reactive<ColumnProps<Menu.Item>[]>([
  // { prop: "appCode", label: "应用编码", search: { el: "input", props: { placeholder: "请输入应用编码" } } },
  { prop: "menuName", label: "菜单名称", search: { el: "input", props: { placeholder: t("请输入") } } },
  { prop: "icon", label: "图标" },
  { prop: "orderNum", label: "排序", width: 160 },
  { prop: "perms", label: "权限标识", width: 200 },
  {
    prop: "status",
    label: "菜单状态",
    tag: true,
    enum: common_status,
    search: { el: "select" },
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 200
  },
  ...(visibleOperationCol(auth.authButtonList, pageButtons)
    ? [
        {
          prop: "operation",
          label: "操作",
          width: getOperationColWidth(auth.authButtonList, pageButtons),
          fixed: "right"
        }
      ]
    : [])
]);

const toggleExpandAll = () => {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  compKey.value++;
};

const getTableList = (params: any) => {
  return getMenuList(params);
};

const dataCallback = (data: any) => {
  return handleTree(data, "menuId", "parentId", "children", true, "orderNum");
};

// 删除用户信息
const delMenu = async (id: number | number[]) => {
  await useHandleData(deleteMenu, { id }, `${t("删除所选菜单")}`);
  proTable.value?.getTableList();
};

const openMenuModal = (title: string, row: Partial<MenuItem> = {}) => {
  // row.menuType = "M";
  // row.status = row.status / 1;
  const params = {
    title,
    form: { ...row },
    isView: title === "查看",
    common_status: common_status.value,
    appList: appList.value,
    api: isEmpty(row.menuId) ? createMenu : editMenu,
    getTableList: proTable.value?.getTableList
  };
  if (title === "新增") {
    Object.assign(params.form, {
      menuType: "M",
      isLink: false,
      isKeepAlive: false,
      isHide: false,
      status: common_status.value[0]?.value,
      isFrame: "1",
      visible: "0",
      isCache: "1"
    });
  } else {
    // params.form.isCache =1;
    // params.form.isFrame = 1;
    // params.form.visible = 1;
  }
  deptModalRef.value?.acceptParams(params);
};

onMounted(() => {
  // _getAppList();
});
</script>
