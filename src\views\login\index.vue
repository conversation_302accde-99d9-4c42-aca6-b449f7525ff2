<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <SwitchDark class="dark" />
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left.png" alt="login" />
      </div>
      <div class="login-form" v-if="forgetPassword">
        <div class="login-logo">
          <!-- <img class="login-icon" src="@/assets/images/logo.svg" alt="" /> -->
          <!--          <h2 class="logo-text">{{ $t("找回密码") }}</h2>-->
        </div>
        <!--        <RetrievePasswordForm v-model:forget-password="forgetPassword" />-->
      </div>
      <div class="login-form" v-else>
        <div class="login-logo">
          <!-- <img class="login-icon" src="@/assets/images/logo.svg" alt="" /> -->
          <h2 class="logo-text">
            <!--            {{ $t("用户登录") }}-->
            Log In
          </h2>
        </div>
        <LoginForm v-model:forget-password="forgetPassword" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import LoginForm from "./components/LoginForm.vue";
// import RetrievePasswordForm from "./components/RetrievePasswordForm.vue";
import SwitchDark from "@/components/SwitchDark/index.vue";
import { ref } from "vue";

const forgetPassword = ref(false);
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
