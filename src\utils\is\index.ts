/**
 * @description: 判断值是否未某个类型
 */
export function is(val: unknown, type: string) {
  return Object.prototype.toString.call(val) === `[object ${type}]`;
}

/**
 * @description:  是否为函数
 */
export function isFunction<T = Function>(val: unknown): val is T {
  return is(val, "Function");
}

/**
 * @description: 是否已定义
 */
export const isDef = <T = unknown>(val?: T): val is T => {
  return typeof val !== "undefined";
};

/**
 * @description: 是否未定义
 */
export const isUnDef = <T = unknown>(val?: T): val is T => {
  return !isDef(val);
};

/**
 * @description: 是否Blob
 */
export const isBlob = (val: any) => {
  return toString.call(val) === "[object Blob]";
};

/**
 * @description: 是否为对象
 */
export const isObject = (val: any): val is Record<any, any> => {
  return val !== null && is(val, "Object");
};

/**
 * @description:  是否为时间
 */
export function isDate(val: unknown): val is Date {
  return is(val, "Date");
}

/**
 * @description:  是否为数值
 */
export function isNumber(val: unknown): val is number {
  return is(val, "Number");
}

/**
 * @description:  是否为AsyncFunction
 */
export function isAsyncFunction<T = any>(val: unknown): val is Promise<T> {
  return is(val, "AsyncFunction");
}

/**
 * @description:  是否为promise
 */
export function isPromise<T = any>(val: unknown): val is Promise<T> {
  return is(val, "Promise") && isObject(val) && isFunction(val.then) && isFunction(val.catch);
}

/**
 * @description:  是否为字符串
 */
export function isString(val: unknown): val is string {
  return is(val, "String");
}

/**
 * @description:  是否为boolean类型
 */
export function isBoolean(val: unknown): val is boolean {
  return is(val, "Boolean");
}

/**
 * @description:  是否为数组
 */
export function isArray(val: any): val is Array<any> {
  return val && Array.isArray(val);
}

/**
 * @description: 是否客户端
 */
export const isClient = () => {
  return typeof window !== "undefined";
};

/**
 * @description: 是否为浏览器
 */
export const isWindow = (val: any): val is Window => {
  return typeof window !== "undefined" && is(val, "Window");
};

/**
 * @description: 是否为 element 元素
 */
export const isElement = (val: unknown): val is Element => {
  return isObject(val) && !!val.tagName;
};

/**
 * @description: 是否为 null
 */
export function isNull(val: unknown): val is null {
  return val === null;
}

/**
 * @description: 是否为 null || undefined
 */
export function isNullOrUnDef(val: unknown): val is null | undefined {
  return isUnDef(val) || isNull(val);
}

/**
 * @description: 是否为 16 进制颜色
 */
export const isHexColor = (str: string) => {
  return /^#?([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/.test(str);
};
// 不严格的判空
export const isEmptyNotStrict = (val: unknown) => {
  return val === "" || val === undefined || val == null || (typeof val === "number" && val === 0);
};

// 严格判空
export const isEmptyStrict = (val: unknown) => {
  return val === null || val === undefined;
};
export const isObj = (v: any) => typeof v === "object" && v !== null;
// 判断对象是否是一个"空"对象
export const isEmptyObj = (obj: any = {}, deep?: boolean): boolean => {
  if (isObj(obj)) {
    return Object.keys(obj).every(key => {
      const val = obj[key];
      // 如果不是深度判空, 则直接返回
      if (!deep) {
        return isEmptyNotStrict(val);
      } else {
        // 如果是深度判空, 并且obj[key]是对象, 则递归调用
        if (isObj(val)) {
          return isEmptyObj(val, true);
        } else {
          // 如果不是对象则直接返回
          return isEmptyNotStrict(val);
        }
      }
    });
  }
  return true;
};

export function isEmpty(v: any, deep?: boolean) {
  if (isObj(v)) return isEmptyObj(v, deep);
  if (Array.isArray(v)) return v.length === 0;
  return isEmptyNotStrict(v);
}

export function isDev() {
  return import.meta.env.VITE_USER_NODE_ENV === "development";
}

export function isProd() {
  return import.meta.env.VITE_USER_NODE_ENV === "production";
}
