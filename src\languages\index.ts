import { createI18n } from "vue-i18n";
import { getBrowserLang } from "@/utils";
import { App } from "vue";
import zh from "./modules/zh";
import en from "./modules/en";

const i18n = createI18n({
  allowComposition: true,
  fallbackLocale: "zh",
  legacy: false,
  locale: getBrowserLang(),
  missingWarn: false,
  fallbackWarn: false,
  messages: {
    zh,
    en
  }
});

// 添加全局 mixin 来处理翻译
export const setupSafeTranslate = (app: App) => {
  app.mixin({
    methods: {
      $t(key: string | null | undefined, ...args: unknown[]) {
        if (!key) return "";
        try {
          return i18n.global.t(key, args);
        } catch (error) {
          console.warn(`Translation key error: ${key}`, error);
          return key;
        }
      }
    }
  });
};

export default i18n;
