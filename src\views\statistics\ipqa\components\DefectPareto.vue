<template>
  <div class="defect-pareto-chart chart-item" ref="defectPareto"></div>
</template>

<script setup lang="ts" name="ipqaChartCard">
import { onMounted, ref } from "vue";
import { ipqaVmiDataParetoAnalysis } from "@/api/modules/productionDataAnalysis";
import { defectParetoChart } from "@/views/statistics/charts";
const props = withDefaults(defineProps<{ queryParams: any }>(), {});

const defectPareto = ref();

const setChart = async () => {
  const { data } = await ipqaVmiDataParetoAnalysis(props.queryParams);
  defectParetoChart({ ref: defectPareto.value, list: data });
};

onMounted(() => {
  setChart();
});
</script>
<style scoped lang="scss">
.tab-wrap {
  text-align: center;
}
.table-main {
  margin-bottom: 10px;
}
.chart-item {
  height: 400px;
  width: 100%;
  margin-top: 30px;
}
</style>
