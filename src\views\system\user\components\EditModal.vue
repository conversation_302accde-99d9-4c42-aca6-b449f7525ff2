<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplier">
            <el-select v-model="form.supplier" :placeholder="$t('请选择')" clearable>
              <el-option v-for="item in supplierList" :key="item.id" :label="item.supplier" :value="item.supplier" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('部门')" prop="deptId">
            <!--            <el-tree-select-->
            <!--              v-model="form.deptId"-->
            <!--              :data="treeDeptList"-->
            <!--              :props="{ value: 'id', label: 'label', children: 'children' }"-->
            <!--              value-key="id"-->
            <!--              placeholder="请选择部门"-->
            <!--              check-strictly-->
            <!--            />-->
            <el-cascader
              style="width: 100%"
              v-model="form.deptId"
              :options="treeDeptList"
              :props="{ label: 'label', value: 'id', children: 'children', checkStrictly: true }"
              ref="cascaderRef"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!form.userId">
          <el-form-item :label="$t('账号')" prop="userName">
            <el-input v-model="form.userName" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('名称')" prop="nickName">
            <el-input v-model="form.nickName" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!form.userId">
          <el-form-item :label="$t('登录密码')" prop="password">
            <el-input v-model="form.password" type="password" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('手机号')" prop="phonenumber">
            <el-input v-model="form.phonenumber" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('邮箱')" prop="email">
            <el-input v-model="form.email" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('职位')" prop="postIds">
            <el-select v-model="form.postIds" :placeholder="t('请选择')" clearable multiple>
              <el-option v-for="item in postList" :key="item.postId" :label="item.postName" :value="item.postId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('角色')" prop="roleIds">
            <el-select v-model="form.roleIds" :placeholder="t('请选择')" clearable multiple>
              <el-option v-for="item in roleList" :key="item.roleId" :label="item.roleName" :value="item.roleId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('状态')" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{ $t(label) }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
import { getDeptAll } from "@/api/modules/dept";
import { getPostAll } from "@/api/modules/post";
import { Staff } from "@/typings/staff";
import { getStaffInfo } from "@/api/modules/staff";
import { useDict } from "@/hooks/useDict";
const supplierList = ref<Supplier.Item[]>([]);
const treeDeptList = ref<any[]>([]);
const roleList = ref<any[]>([]);
const postList = ref<any[]>([]);
const cascaderRef = ref();

const { common_status } = useDict("common_status");
const { t } = useI18n();
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Staff.staffForm>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  userName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  loginName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  password: [{ required: true, message: t("请填写"), trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项
const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const getDeptLists = async () => {
  const { data } = await getDeptAll();
  treeDeptList.value = data;
};
const getPosts = async () => {
  const { posts, roles } = await getPostAll();
  roleList.value = roles;
  postList.value = posts;
};

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  if (!params.form.userId) {
    params.form.password = "123456";
    await getPosts();
  } else {
    const { data, posts, roles, postIds, roleIds } = await getStaffInfo(params.form.userId);
    form.value = data;

    postList.value = posts;
    roleList.value = roles;
    form.value.postIds = postIds;
    form.value.roleIds = roleIds;
  }
  await getSupplierData();
  await getDeptLists();
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const _form = JSON.parse(JSON.stringify(form.value));
      // if (!_form.password) {
      //   _form.password = "123456";
      // }
      const depts = cascaderRef.value.getCheckedNodes();
      if (depts.length > 0) {
        _form.deptId = depts[0].value;
        _form.deptName = depts[0].label;
      }

      // delete _form.dept;
      // delete _form.roles;

      await state.api!(_form);
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
