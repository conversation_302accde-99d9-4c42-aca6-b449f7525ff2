<template>
  <el-dialog v-model="visible" width="580px" :destroy-on-close="true" :title="`${t('转办')}`">
    <el-form ref="formRef" label-width="auto" label-suffix=" :" :rules="rules" :model="form" :show-message="isZh">
      <el-row>
        <el-col :span="16">
          <el-form-item :label="$t('转办审批人')" prop="auditorName">
            <RemoteSearch :job-num="form.auditorNo" v-model="form.auditorName" @extra="auditorExtra"></RemoteSearch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="DelayModal">
import { Transfer } from "@/typings/transfer";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { ref, reactive, toRefs } from "vue";
import { useI18n } from "vue-i18n";
import RemoteSearch from "./RemoteSearch.vue";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { updateUserSearchExtra } from "@/utils";
import { Staff } from "@/typings/staff";
const { isZh } = useLanguageCode();
const { t } = useI18n();

interface IState {
  form: Partial<Transfer.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  auditorName: [{ required: true, message: "请选择转办审批人", trigger: "blur" }]
};

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  form: {}
});
const { form } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

const auditorExtra = (staff: Staff.Extra) => {
  updateUserSearchExtra(form, staff, "auditor", "审批人没有绑定邮箱,请更换");
};

const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      ElMessageBox.confirm(t("确定转给") + "[" + form.value.auditorNo + "]" + form.value.auditorName, t("提示"), {
        confirmButtonText: t("确认"),
        cancelButtonText: t("取消"),
        type: ""
      }).then(async () => {
        await state.api!(form.value);
        ElMessage.success({ message: t(`保存成功`) });
        state.getTableList!();
        setVisible(false);
      });
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
<style>
#plan-table .card {
  padding: 0;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}
</style>
