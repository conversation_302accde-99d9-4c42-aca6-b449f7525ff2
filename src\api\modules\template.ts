import { ReqPage, ResPage } from "@/api/interface/index";
import { Template } from "@/typings/template";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

// 列表
export const getTemplateList = (params?: ReqPage) => {
  return http.post<ResPage<Template.Item>>(`${API_PREFIX}/supplierTemplate/list`, params);
};
// add
export const createTemplate = (params?: ReqPage) => {
  return http.post<ResPage<Template.Item>>(`${API_PREFIX}/supplierTemplate/save`, params);
};
// edit
export const getTemplateInfo = (params?: string) => {
  return http.post<ResPage<Template.Item>>(`${API_PREFIX}/supplierTemplate/get/` + params);
};
// remove
export const templateListRemove = (params: any[]) => {
  return http.post(`${API_PREFIX}/supplierTemplate/del`, params);
};
