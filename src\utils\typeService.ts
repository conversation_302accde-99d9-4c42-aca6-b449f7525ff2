import { ref } from "vue";
import { getQualityPersonQualificationTypeList } from "@/api/modules/quality-person/quality_person_qualification_type";
import { QualityPersonQualificationType } from "@/typings/quality-person/quality_person_qualification_type";

export const qualificationTypeList = ref<QualityPersonQualificationType.Item[]>([]);
export const getQualificationTypeList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getQualityPersonQualificationTypeList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      qualificationTypeList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
