<script setup lang="ts">
import { Upload } from "@/api/interface";
import { getFileDetail } from "@/api/modules/upload";
import { formatUrl } from "@/utils";
import { onMounted, ref, computed } from "vue";
interface IProps {
  id: number;
  isImage?: boolean;
  showDelete?: boolean;
  linktype?: "primary" | "success" | "warning" | "danger" | "info" | "default";
}
interface IEmits {
  (e: "handleRemove", id: number): void;
}
const props = withDefaults(defineProps<IProps>(), {
  isImage: false,
  showDelete: true,
  linktype: "default"
});
const emits = defineEmits<IEmits>();
const file = ref<Upload.ResFileUrl>({} as Upload.ResFileUrl);
const id = computed(() => props.id);
const isImage = computed(() => props.isImage);
const getFile = async () => {
  const { data } = await getFileDetail(id.value);
  file.value = data;
};
onMounted(async () => {
  await getFile();
});

const handleRemove = () => {
  emits("handleRemove", id.value);
};
</script>

<template>
  <el-image v-if="isImage" :src="formatUrl(file.path)" :preview-src-list="[formatUrl(file.path)]" fit="cover" />
  <li v-else class="px-2 list-none el-upload-list__item ele-upload-list__item-content sle">
    <el-link :type="linktype" :href="formatUrl(file.path)" class="w-full sle !text-ellipsis" :underline="false" target="_blank">
      {{ file.realName }}
    </el-link>
    <div v-if="showDelete" class="ele-upload-list__item-content-action">
      <el-link :underline="false" @click="handleRemove" type="danger">删除</el-link>
    </div>
  </li>
</template>
