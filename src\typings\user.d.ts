import { Dept } from "./dept";
export namespace User {
  export interface Item {
    id: number;
    name: string;
    avatar: string;
    jobNum: number; // 工号
    tel: string; // 手机号
    languageCode: string;
    password?: string; // 密码;
    email: string; // 邮箱
    rank?: string; // 职级 目前先是字符串 可能需要维护字典
    postId: number; // 职位id
    roleId: number;
    deptId: number;
    deptParent: number;
    company: number; // 公司/ 工厂
    leader: number; // 直属领导
    status: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    auditStatus: string;
    createdTime: string;
  }
}
