import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Role } from "@/typings/role";
import { API_PREFIX } from "@/api/config/servicePort";

export const getRoleList = (params?: ReqPage) => {
  return http.get<ResPage<Role.Item>>(`${API_PREFIX}/system/role/list`, params);
};

export const getRoleDetail = (roleId: number) => {
  return http.get<Role.Item>(`${API_PREFIX}/system/role/${roleId}`);
};

export const createRole = (data: Role.Item) => {
  return http.post(`${API_PREFIX}/system/role`, data);
};

export const editRole = (data: Role.Item) => {
  return http.put(`${API_PREFIX}/system/role`, data);
};

export const deleteRole = (data: string) => {
  return http.delete(`${API_PREFIX}/system/role/` + data);
};

export const roleMenuTreeselect = (roleId: number) => {
  return http.get<any>(`${API_PREFIX}//system/menu/roleMenuTreeselect/` + roleId);
};
