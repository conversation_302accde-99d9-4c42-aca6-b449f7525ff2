<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'exception:add'" type="primary" @click="handleModalOperation('新增')">{{ $t("新增") }}</el-button>
        <!--        <el-button type="primary" @click="openRoleModal('编辑')">{{ $t("提醒") }}</el-button>-->
        <el-button v-auth="'exception:export'" type="primary" @click="handleExport">{{ $t("导出") }}</el-button>
      </template>
      <template #reportNo="{ row }">
        <el-link type="primary" @click="handleExceptionInfo(row)">{{ row.reportNo }}</el-link>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button
          v-auth="'exception:remove'"
          type="danger"
          plain
          :disabled="!isSelected"
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("批量删除") }}
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button v-auth="'exception:edit'" type="primary" link @click="handleModalOperation('编辑', row)">
          {{ $t("编辑") }}
        </el-button>
        <el-button v-auth="'exception:remove'" type="danger" link @click="handleRemove(row)">{{ $t("删除") }}</el-button>
      </template>
    </ProTable>
    <handleModal ref="handleModalRef" />
    <exceptionInfoModal ref="exceptionInfoModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import {
  getExceptionManage,
  exceptionManageDelete,
  exceptionManageExport,
  exceptionManageAdd,
  exceptionManageSave
} from "@/api/modules/exception-manage";
import { ExceptionManageItem, ExceptionManageQuery } from "@/typings/exception-manage";
import { ref, reactive, onMounted, nextTick } from "vue";
import { isArray, isEmpty } from "@/utils/is";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { downloadFileByName } from "@/utils/download";
import handleModal from "./components/handleModal.vue";
import exceptionInfoModal from "@/views/components/exceptionInfoModal.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const handleModalRef = ref<InstanceType<typeof handleModal> | null>();
const exceptionInfoModalRef = ref<InstanceType<typeof exceptionInfoModal> | null>();
const proTable = ref<ProTableInstance>();

const { product_names, report_exception_type, report_node_type_8d, common_status, report_status } = useDict(
  "product_names",
  "report_exception_type",
  "report_node_type_8d",
  "common_status",
  "report_status"
);

// const supplierList = ref<Supplier.Item[]>();
const { check, currentRow } = useCheckSelectId();

const initParam = reactive({
  pageSize: 10,
  pageNum: 1,
  condition: {
    startDate: "",
    endDate: "",
    reportNo: "",
    supplier: "",
    reportStatus: "",
    product: "",
    createBy: "",
    title: "",
    exceptionType: ""
  }
});

import { getOperationColWidth, visibleOperationCol } from "@/utils";
import { useAuthStore } from "@/stores/modules/auth";
import { useDict } from "@/hooks/useDict";
import DateRange from "@/views/components/DateRange.vue";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
import { formatParams } from "@/utils/util";
import { getPublicUser } from "@/api/modules/staff";
import { Staff } from "@/typings/staff";

const supplierList = ref<Supplier.Item[]>([]);
const userList = ref<Staff.Item[]>([]);

const pageButtons = ["exception:edit", "exception:remove"];
const auth = useAuthStore();
const columns = reactive<ColumnProps<ExceptionManageItem>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "reportNo",
    label: "异常编号",
    search: { el: "input", render: () => <el-input v-model={initParam.reportNo} clearable placeholder={t("请输入")} /> },
    width: 200
  },
  {
    prop: "title",
    label: "异常标题",
    search: { el: "input", render: () => <el-input v-model={initParam.title} clearable placeholder={t("请输入")} /> },
    width: 300
  },
  {
    prop: "supplierName",
    label: "供应商",
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "product",
    label: "产品名称",
    enum: product_names,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.product} placeholder={t("请选择")} clearable>
            {product_names.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "reportStatus",
    label: "状态",
    enum: report_status,
    width: 150,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.reportStatus} placeholder={t("请选择")} clearable>
            {report_status.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "exceptionType",
    label: "异常类型",
    enum: report_exception_type,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.exceptionType} placeholder={t("请选择")} clearable>
            {report_exception_type.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "sourceNo", label: "NCMR", width: 120 },
  { prop: "currentNodeName", label: "当前节点", width: 200 },
  { prop: "day", label: "已用时(天)", width: 200 },
  { prop: "responsibleBy", label: "责任人", width: 200 },
  {
    prop: "createBy",
    label: "创建人",
    enum: userList.value,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.createBy} placeholder={t("请选择")} clearable>
            {userList.value.map(item => (
              <el-option key={item.id} label={item.nickName} value={item.nickName} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 200,
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  ...(visibleOperationCol(auth.authButtonList, pageButtons)
    ? [
        {
          prop: "operation",
          label: "操作",
          width: getOperationColWidth(auth.authButtonList, pageButtons),
          fixed: "right"
        }
      ]
    : [])
]);
let queryParams = reactive<ExceptionManageQuery.IQueryParams>({} as ExceptionManageQuery.IQueryParams);
const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  // const { pageNum, pageSize } = params;
  // const { ...condition } = initParam;
  // initParam.pageNum = pageNum;
  // initParam.pageSize = pageSize;
  // if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
  //   condition.startDate = filterDate.value[0];
  //   condition.endDate = filterDate.value[1];
  // }
  //
  // queryParams = reactive(initParam);
  const result = formatParams(initParam, params, filterDate.value);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return getExceptionManage(result);
};

// 批量删除用户信息
const batchDelete = async (id: number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(exceptionManageDelete, { ids }, "删除所选信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

/**
 * 删除
 * @param row
 */
const handleRemove = async (row: ExceptionManageItem) => {
  await useHandleData(exceptionManageDelete, { ids: [row.id] }, `删除所选数据`);
  proTable.value?.getTableList();
};

/**
 * 导出
 * @param row
 */
const handleExport = async (row: ExceptionManageItem) => {
  await downloadFileByName(exceptionManageExport, initParam);
};
/**
 * 新增
 */
const handleModalOperation = (title: string, row?: ExceptionManageItem) => {
  const form = isEmpty(row?.id) ? {} : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: isEmpty(row?.id) ? exceptionManageAdd : exceptionManageSave,
    getTableList: proTable.value?.getTableList,
    fileUrls: !isEmpty(row?.id) ? [{ name: row?.attachmentName, url: row?.attachmentUrl }] : []
  };
  console.log(isEmpty(row?.id), "isEmpty(row?.id)");
  if (isEmpty(row?.id)) {
    Object.assign(params.form, {
      exceptionType: report_exception_type.value[0]?.value
    });
  }

  handleModalRef.value?.acceptParams(params);
};

const handleExceptionInfo = (row: ExceptionManageItem) => {
  const params = {
    title: "异常查看",
    isView: true,
    form: row,
    getTableList: proTable.value?.getTableList,
    handleType: "view"
  };
  exceptionInfoModalRef.value?.acceptParams(params);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};
const getStaffListAll = async () => {
  const { data } = await getPublicUser({ pageNum: 1, pageSize: 999 });
  userList.value = data.list;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};
onMounted(async () => {
  await getSupplierData();
  await getStaffListAll();
});
</script>
