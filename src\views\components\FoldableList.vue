<template>
  <div class="card foldable-list" :class="{ 'is-collapsed': isCollapsed }">
    <div class="collapse-handle" @click="toggleCollapse">
      <el-icon :size="15">
        <ArrowLeft v-if="!isCollapsed" />
        <ArrowRight v-else />
      </el-icon>
    </div>

    <div class="list-container">
      <div class="list-content">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="list-item"
          :class="{
            'vertical-text': isCollapsed,
            'is-selected': isSelected(item)
          }"
          @click="handleItemClick(item)"
        >
          <span class="item-content">{{ item[labelKey] || item }}</span>
          <span v-if="isCollapsed && isSelected(item)" class="dot-indicator"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, onMounted } from "vue";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";

interface Props {
  data: Array<any>;
  title?: string;
  labelKey?: string;
  defaultCollapsed?: boolean;
  modelValue?: any;
  storageKey?: string; // New prop for local storage key
}

const props = withDefaults(defineProps<Props>(), {
  labelKey: "label",
  defaultCollapsed: false,
  modelValue: null,
  storageKey: "foldableListCollapsedState"
});

const emit = defineEmits(["itemClick", "update:modelValue"]);

const { modelValue } = toRefs(props);
const isCollapsed = ref(props.defaultCollapsed);

// Load collapsed state from local storage on component mount
onMounted(() => {
  const savedState = localStorage.getItem(props.storageKey);
  if (savedState !== null) {
    isCollapsed.value = JSON.parse(savedState);
  }
});

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  // Save the new state to local storage
  localStorage.setItem(props.storageKey, JSON.stringify(isCollapsed.value));
};

const isSelected = (item: any) => {
  if (!modelValue.value) return false;
  if (typeof item === "object") {
    return item[props.labelKey] === modelValue.value?.[props.labelKey];
  }
  return item === modelValue.value;
};

const handleItemClick = (item: any) => {
  emit("update:modelValue", item);
  emit("itemClick", item);
};
</script>

<style scoped lang="scss">
.foldable-list {
  position: relative;
  box-sizing: border-box;
  width: 160px;
  height: 100%;
  padding: 16px;
  margin-right: 10px;
  overflow: visible;
  color: #606266;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
  &.is-collapsed {
    width: 40px;
    padding: 16px 4px;
    .list-item {
      padding: 8px 4px;
      margin: 6px auto;
      background-color: #f2f2f2;
      writing-mode: vertical-rl;
      text-orientation: mixed;
      &.is-selected {
        position: relative;
        .dot-indicator {
          position: absolute;
          bottom: -8px;
          left: 50%;
          width: 6px;
          height: 6px;
          background-color: #409eff;
          border-radius: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
  .collapse-handle {
    position: absolute;
    top: 50%;
    right: -10px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10px;
    height: 48px;
    color: #606266;
    cursor: pointer;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-left: none;
    border-radius: 0 6px 6px 0;
    box-shadow: 2px 0 4px rgb(0 0 0 / 5%);
    transition: all 0.2s;
    transform: translateY(-50%);
    &:hover {
      color: #409eff;
      background: #f5f7fa;
      border-color: #c0c4cc;
    }
    .el-icon {
      transition: transform 0.2s;
    }
  }
  .list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .list-content {
    flex: 1;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #c0c4cc;
      border-radius: 2px;
    }
  }
  .list-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 4px 12px;
    margin: 4px 0;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    .item-content {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:hover {
      background-color: #f5f7fa;
    }
    &.is-selected {
      font-weight: 500;
      color: #ffffff;
      background-color: #5c64e1;
    }
    &.vertical-text {
      justify-content: center;
      text-align: center;
      letter-spacing: 2px;
      .item-content {
        white-space: normal;
      }
    }
    .dot-indicator {
      display: none;
    }
  }
}
</style>
