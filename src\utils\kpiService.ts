import { ref } from "vue";
import { getWpKpiList } from "@/api/modules/quality-work-plan/wp_kpi";
import { WpKpi } from "@/typings/quality-work-plan/wp_kpi";

export const kpiList = ref<WpKpi.Item[]>([]);
export const getKpiList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getWpKpiList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      kpiList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
