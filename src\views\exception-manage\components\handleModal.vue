<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form ref="formRef" label-width="170px" label-suffix=" :" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row>
        <el-col :span="24" v-if="!!form.id">
          <el-form-item :label="$t('异常编号')">
            {{ form.reportNo }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('异常标题')" prop="title">
            <el-input :disabled="onlyUser" v-model="form.title" :placeholder="$t('请填写')" clearable />
          </el-form-item>
          <el-form-item :label="$t('异常类型')" prop="exceptionType">
            <el-radio-group :disabled="onlyUser" v-model="form.exceptionType">
              <el-radio v-for="(item, index) in report_exception_type" :key="index" :label="item.value">{{
                item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <!--            <el-input :disabled="onlyUser" v-model="form.supplierName" :placeholder="$t('请填写')" clearable />-->
            <el-select :disabled="onlyUser" v-model="form.supplierName" :placeholder="$t('请选择')" clearable>
              <el-option v-for="item in supplierList" :key="item.id" :label="item.supplier" :value="item.supplier" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('产品名称')" prop="product">
            <el-select :disabled="onlyUser" v-model="form.product">
              <el-option v-for="{ label, value } of product_names" :key="value" :label="label" :value="value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('责任人')" prop="responsibleBy">
            <el-select v-model="form.responsibleBy">
              <el-option v-for="{ nickName, userId } of responsibleList" :key="userId" :label="nickName" :value="nickName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('来源')">
            <el-select :disabled="onlyUser" v-model="form.source">
              <el-option v-for="{ label, value } of report_source" :key="value" :label="label" :value="value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('来源编号')">
            <el-input :disabled="onlyUser" v-model="form.sourceNo" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('异常描述')">
            <el-input
              :disabled="onlyUser"
              :autosize="{ minRows: 4, maxRows: 8 }"
              type="textarea"
              v-model="form.exceptionDescription"
              :placeholder="$t('请填写')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('上传附件')">
            <UploadFiles
              v-if="!onlyUser || (onlyUser && fileUrls.length > 0)"
              :show-delete="!onlyUser"
              :disabled="onlyUser"
              :file-url="fileUrls"
              :multiple="false"
              :is-show-tip="true"
              :immediate="true"
              :limit="1"
              :file-type="['pdf', 'xls', 'xlsx']"
              :btn-name="$t('上传附件')"
              @upload-success="refFileSuccess"
              @file-list-empty="refFileEmpty"
            />
            <span v-else>{{ $t("未上传任何附件") }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.id">
          <el-form-item :label="$t('提交人')"> {{ form.submitterBy }} </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.id">
          <el-form-item :label="$t('工号')"> {{ form.submitterNo }} </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.id">
          <el-form-item :label="$t('提交时间')"> {{ form.createTime }} </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.id">
          <el-form-item :label="$t('更新时间')"> {{ form.updateTime }} </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="RoleModal">
import { ExceptionManageForm } from "@/typings/exception-manage";
import { getPublicUser } from "@/api/modules/staff";
import { ElMessage, FormInstance, ElTreeSelect } from "element-plus";
import UploadFiles from "@/components/Upload/UploadFiles.vue";
import { ref, reactive, toRefs } from "vue";
import { isEmpty } from "@/utils/is";
import { useI18n } from "vue-i18n";
import { useDict } from "@/hooks/useDict";
import { getUserList } from "@/api/modules/user";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
const { product_names, report_exception_type, report_node_type_8d, common_status, report_source } = useDict(
  "product_names",
  "report_exception_type",
  "report_node_type_8d",
  "common_status",
  "report_source"
);
interface IState {
  title: string;
  isView: boolean;
  form: Partial<ExceptionManageForm>;
  // common_status: Dict.IDataItem[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  fileUrls?: any[];
  responsibleList: any[];
  onlyUser: Boolean;
}

const { t } = useI18n();
const supplierList = ref<Supplier.Item[]>([]);

const rules = {
  title: [{ required: true, message: t("请选择"), trigger: "blur" }],
  exceptionType: [{ required: true, message: t("请选择"), trigger: "blur" }],
  supplierName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  product: [{ required: true, message: t("请选择"), trigger: "blur" }],
  responsibleBy: [{ required: true, message: t("请选择"), trigger: "blur" }]
};

// const treeSelectRef = ref<InstanceType<typeof ElTreeSelect> | null>();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  // common_status: [],
  form: {},
  fileUrls: [],
  responsibleList: [],
  onlyUser: false
});

// const menuList = ref<Menu.Item[]>([]);

const { form, title, isView, fileUrls, responsibleList, onlyUser } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

// const getAllMenuList = async () => {
//   const { data } = await getMenuList();
//   menuList.value = handleTree(data, "menuId", "parentId", "children", true, "orderNum");
// };
const refFileSuccess = (res: { name: string; url: string }) => {
  form.value.attachmentName = res.name;
  form.value.attachmentUrl = res.url;
};

const refFileEmpty = () => {
  form.value.attachmentName = "";
  form.value.attachmentUrl = "";
};
const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};
const getAllStaff = async () => {
  const { data } = await getPublicUser({ pageNum: 1, pageSize: 999 });
  responsibleList.value = data.list;
};
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  await getAllStaff();
  await getSupplierData();
  setVisible(true);
};
// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({
        ...form.value
      });
      ElMessage.success({ message: t(`${title.value}${t("成功！")}`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style>
.tree-border {
  width: 100%;
  margin-top: 5px;
  background: #ffffff none;
  border: 1px solid #e5e6e7;
  border-radius: 4px;
}
</style>
