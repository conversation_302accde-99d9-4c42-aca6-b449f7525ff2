import { RouteRecordRaw } from "vue-router";
import { HOME_URL, LOGIN_URL, REGISTER_URL, SCORE_URL } from "@/config";
import Layout from "@/layouts/index.vue";
class RouteOption {}

/**
 * staticRouter (静态路由)
 */
export const staticRouter: RouteOption[] = [
  // {
  //   path: "/",
  //   redirect: HOME_URL
  // },
  {
    path: LOGIN_URL,
    name: "login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录"
    }
  },

  {
    path: REGISTER_URL,
    name: "register",
    component: () => import("@/views/register/index.vue"),
    meta: {
      title: "注册"
    }
  },
  {
    path: "/layout",
    name: "layout",
    component: () => import("@/layouts/index.vue"),
    // component: () => import("@/layouts/indexAsync.vue"),
    redirect: HOME_URL,
    children: []
  }
  // {
  //   path: "/system/dict-data",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["dict-data"],
  //   children: [
  //     {
  //       path: "index/:dictId(\\d+)",
  //       component: () => import("@/views/system/dict-data/index.vue"),
  //       name: "Data",
  //       meta: { title: "字典数据", activeMenu: "/system/dict" }
  //     }
  //   ]
  // }
];

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
  {
    path: "/403",
    name: "403",
    component: () => import("@/components/ErrorMessage/403.vue"),
    meta: {
      title: "403页面"
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/components/ErrorMessage/404.vue"),
    meta: {
      title: "404页面"
    }
  },
  {
    path: "/500",
    name: "500",
    component: () => import("@/components/ErrorMessage/500.vue"),
    meta: {
      title: "500页面"
    }
  },
  // Resolve refresh page, route warnings
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/components/ErrorMessage/403.vue")
  }
];
