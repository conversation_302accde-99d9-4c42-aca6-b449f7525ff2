<template>
  <div class="el-week-picker">
    <el-popover :visible="visible" width="500" placement="bottom-start" trigger="click">
      <div>
        <div class="top-box">
          <el-icon @click="preYear">
            <DArrowLeft />
          </el-icon>
          <span>{{ currYear }} 年</span>
          <el-icon @click="nextYear">
            <DArrowRight />
          </el-icon>
        </div>
        <div class="content-box">
          <span :class="{ selected: i === currWeek }" @click="handleWeek(i)" v-for="i in weekList" :key="i">
            {{ i }}
          </span>
        </div>
      </div>
      <template #reference>
        <el-input
          v-model="innerValue"
          @focus="onFocus"
          ref="inputRef"
          @blur="onBlur"
          :placeholder="placeholder"
          :prefix-icon="Calendar"
          :readonly="true"
        >
          <template #suffix>
            <el-icon @click="clear" v-show="clearVisible"><CircleClose /></el-icon>
          </template>
        </el-input>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from "vue";
import { Calendar, DArrowRight, DArrowLeft, CircleClose } from "@element-plus/icons-vue";

const props = defineProps({
  placeholder: {
    type: String,
    default: "选择周"
  },
  modelValue: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["change", "update:modelValue"]);

const innerValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

const visible = ref(false);
const currYear = ref<number>(new Date().getFullYear()); // 默认当前年
const weekList = ref<string[]>([]);
const currWeek = ref<string>("");

// 获取某一年的周列表
const getWeekList = (year: number) => {
  const weeks = [];
  const firstDayOfYear = new Date(year, 0, 1);
  const lastDayOfYear = new Date(year, 11, 31);

  let currentDay = firstDayOfYear;
  let weekNumber = 1;

  while (currentDay <= lastDayOfYear) {
    weeks.push(`${String(weekNumber).padStart(2, "0")}`);
    currentDay.setDate(currentDay.getDate() + 7);
    weekNumber++;
  }
  return weeks;
};

// 更新周列表
const updateWeekList = () => {
  weekList.value = getWeekList(currYear.value);
};

// 初始化时根据传入的 modelValue 设置 currYear 和 currWeek
const initFromModelValue = () => {
  if (props.modelValue) {
    const [year, week] = props.modelValue.split("-");
    currYear.value = parseInt(year);
    currWeek.value = week;
  }
  updateWeekList(); // 更新周列表
};

// 组件挂载时初始化
onMounted(() => {
  initFromModelValue();
});

const inputRef = ref<any>(null);
const inputFocus = () => {
  inputRef.value?.focus();
};
defineExpose({
  inputFocus
});

// 切换到下一年
const nextYear = () => {
  currYear.value += 1;
  updateWeekList(); // 仅更新周列表，不更改已选择的周
};

// 切换到上一年
const preYear = () => {
  currYear.value -= 1;
  updateWeekList(); // 仅更新周列表，不更改已选择的周
};

// 处理周选择
const handleWeek = (week: string) => {
  currWeek.value = week;
  updateInnerValue();
  emitChange();
  visible.value = false;
  inputRef.value.blur(); // 选择周后取消输入框焦点
};

// 清空选择
const clearVisible = ref(false);
const clear = () => {
  innerValue.value = "";
  currWeek.value = "";
  emit("change", { startDate: "", endDate: "" });
};

// 输入框聚焦时触发
const onFocus = () => {
  visible.value = true;
};

// 输入框失焦时触发
const onBlur = () => {
  // 使用 setTimeout 延迟隐藏弹框，避免与点击弹框内部元素冲突
  setTimeout(() => {
    visible.value = false;
  }, 10);
};

// 监听 innerValue 的变化
watch(
  () => innerValue.value,
  val => {
    initFromModelValue();
    nextTick(() => {
      clearVisible.value = !!val;
    });
  }
);

// 更新 innerValue
const updateInnerValue = () => {
  innerValue.value = `${currYear.value}-${currWeek.value}`;
};

// 触发 change 事件
const emitChange = () => {
  const emitDate = {
    startDate: `${currYear.value}-${currWeek.value}`,
    endDate: `${currYear.value}-${currWeek.value}`
  };
  emit("change", emitDate);
};
</script>

<style scoped>
.el-week-picker {
  display: inline-block;
}
.top-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 12px;
  font-size: 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
.content-box {
  display: grid;
  grid-template-columns: repeat(10, 1fr); /* 10 columns */
  gap: 8px;
  padding-top: 12px;
}
.content-box > span {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  text-align: center;
  cursor: pointer;
}
.content-box > span.selected {
  color: white;
  background-color: var(--el-color-primary);
}
</style>
