<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <template #reportNo="{ row }">
        <el-link type="primary" @click="handleExceptionInfo('view', row)">{{ row.reportNo }}</el-link>
      </template>
      <template #tableHeader>
        <el-button v-auth="'reportNode:input'" type="primary" @click="handleExceptionInfo('input')">{{ $t("填报") }}</el-button>
        <el-button v-auth="'reportNode:setpeople'" type="primary" @click="setPeople('转办')">{{ $t("转办") }}</el-button>
      </template>
    </ProTable>
    <exceptionInfoModal ref="exceptionInfoModalRef" />
    <handleModal ref="handleModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { exceptionManageAdd, exceptionManageSave, exceptionReportsSupplierList } from "@/api/modules/exception-manage";
import { ref, reactive, onMounted, nextTick } from "vue";
import DateRange from "@/views/components/DateRange.vue";
import handleModal from "@/views/exception-manage/components/handleModal.vue";
import { Role } from "@/typings/role";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";

import exceptionInfoModal from "@/views/components/exceptionInfoModal.vue";
import { ExceptionManageItem, ExceptionManageQuery } from "@/typings/exception-manage";
const proTable = ref<ProTableInstance>();
const handleModalRef = ref();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();
const initParam = reactive({ pageNum: 1, pageSize: 10 });
const { product_names, report_exception_type, report_node_type_8d, common_status, report_status } = useDict(
  "product_names",
  "report_exception_type",
  "report_node_type_8d",
  "common_status",
  "report_status"
);
import { useAuthStore } from "@/stores/modules/auth";
import { isEmpty } from "@/utils/is";
import { useDict } from "@/hooks/useDict";
import { Staff } from "@/typings/staff";
import { getPublicUser } from "@/api/modules/staff";
import { formatParams } from "@/utils/util";
import { useI18n } from "vue-i18n";
const pageButtons = ["exception:edit", "exception:remove"];
const auth = useAuthStore();
const userList = ref<Staff.Item[]>([]);

const columns = reactive<ColumnProps<Role.Item>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "reportNo",
    label: "异常编号",
    search: { el: "input", render: () => <el-input v-model={initParam.reportNo} clearable placeholder={t("请输入")} /> },
    width: 200
  },
  {
    prop: "title",
    label: "异常标题",
    search: { el: "input", render: () => <el-input v-model={initParam.title} clearable placeholder={t("请输入")} /> },
    width: 300
  },
  {
    prop: "product",
    label: "产品名称",
    enum: product_names,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.product} placeholder={t("请选择")} clearable>
            {product_names.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "reportStatus",
    label: "状态",
    enum: report_status,
    width: 150,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.reportStatus} placeholder={t("请选择")} clearable>
            {report_status.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "exceptionType",
    label: "异常类型",
    enum: report_exception_type,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.exceptionType} placeholder={t("请选择")} clearable>
            {report_exception_type.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "currentNodeName", label: "当前节点", width: 200 },
  { prop: "day", label: "已用时(天)", width: 200 },
  { prop: "responsibleBy", label: "责任人", width: 200 },
  {
    prop: "createBy",
    label: "创建人",
    enum: userList.value,
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.createBy} placeholder={t("请选择")} clearable>
            {userList.value.map(item => (
              <el-option key={item.id} label={item.nickName} value={item.nickName} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 200,
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  }
]);

const exceptionInfoModalRef = ref<InstanceType<typeof exceptionInfoModal> | null>();

let queryParams = reactive<ExceptionManageQuery.IQueryParams>({} as ExceptionManageQuery.IQueryParams);
const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  // const { pageNum, pageSize, ...condition } = params;
  //
  // if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
  //   condition.startDate = filterDate.value[0];
  //   condition.endDate = filterDate.value[1];
  // }
  //
  // queryParams = reactive(condition);
  const result = formatParams(initParam, params, filterDate.value);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return exceptionReportsSupplierList(result);
};

const handleExceptionInfo = (type, row?: ExceptionManageItem) => {
  let paramsForm = row;
  let title = "查看";
  let isView = true;
  if (type !== "view") {
    check();
    paramsForm = currentRow.value;
    title = "异常填报";
    isView = false;
  }
  const params = {
    title,
    isView,
    form: paramsForm,
    getTableList: proTable.value?.getTableList,
    handleType: type
  };
  exceptionInfoModalRef.value?.acceptParams(params);
};

const setPeople = (title: string) => {
  check();
  const row = currentRow.value;
  const form = isEmpty(row?.id) ? {} : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: exceptionManageSave,
    getTableList: proTable.value?.getTableList,
    fileUrls: !isEmpty(row?.id) ? [{ name: row?.attachmentName, url: row?.attachmentUrl }] : []
  };

  params.onlyUser = true;
  handleModalRef.value?.acceptParams(params);
};

const getStaffListAll = async () => {
  const { data } = await getPublicUser({ pageNum: 1, pageSize: 999 });
  userList.value = data.list;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};
onMounted(async () => {
  await getStaffListAll();
});
</script>
