import { defineStore } from "pinia";
import { StaffState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { Staff } from "@/typings/staff";
import { getStaffAll } from "@/api/modules/staff";

const useStaffStore = defineStore({
  id: "staff",
  state: (): StaffState => ({
    staff: [] as Staff.Item[]
  }),
  getters: {},
  actions: {
    setStaff(staff: Staff.Item[]) {
      this.staff = staff;
    },
    async getStaff() {
      const { data } = await getStaffAll();
      const staff = data.map(user => ({
        jobNum: user.jobNum,
        name: user.name,
        email: user.email,
        id: user.id,
        deptId: user.deptId,
        leader: user.leader,
        indirectLeader: user.indirectLeader,
        postId: user.postId
      }));
      this.setStaff(staff as any);
    }
  },
  persist: piniaPersistConfig("staff")
});

export default useStaffStore;
