import { defineStore } from "pinia";
import { formulaState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { Statistics } from "@/typings/customer-refund/statistics";
import { getFormulaExplanationList } from "@/api/modules/common";
import { APP_CODE } from "@/config";

const useFormulaStore = defineStore({
  id: "formula",
  state: (): formulaState => ({
    formula: [] as Statistics.FormulaExplanation[]
  }),
  getters: {},
  actions: {
    setFormula(formula: Statistics.FormulaExplanation[]) {
      this.formula = formula;
    },
    async getFormula() {
      // const {
      //   data: { list }
      // } = await getFormulaExplanationList({
      //   condition: {
      //     //code: APP_CODE
      //   },
      //   pageNum: 1,
      //   pageSize: 1000
      // });
      // this.setFormula(list as any);
    },
    getFormulaContent(code: string) {
      return this.formula.find(item => item.code === code)?.content ?? "";
    }
  },
  persist: piniaPersistConfig("formula")
});

export default useFormulaStore;
