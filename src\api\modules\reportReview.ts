import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { DimensionsQuery } from "@/typings/dimensions";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

export const getReportReviewList = (params: ReqPage) => {
  return http.get<ResPage<any>>(`${API_PREFIX}/reportReviewList`, params);
};

export const exceptionReportsApprove = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/exceptionReports/node/approve`, params);
};

export const exceptionReportsReject = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/exceptionReports/node/reject`, params);
};
