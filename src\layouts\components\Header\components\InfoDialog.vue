<template>
  <el-dialog v-model="visible" :title="$t('个人信息')" width="500px" draggable>
    <el-descriptions :column="2">
      <template v-for="({ label, value, field }, index) of showInfo" :key="index">
        <el-descriptions-item v-if="value || userInfo[field]" :label="$t(label) + ' :'">
          {{ $t((value ?? userInfo[field]) as string) }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
        <el-button type="primary" @click="setVisible(true)">{{ $t("确认") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import useUserStore from "@/stores/modules/user";
import { Staff } from "@/typings/staff";
import { Post } from "@/typings/post";
import { getPostList } from "@/api/modules/post";
import { useDict } from "@/hooks/useDict";
import { getStaffList } from "@/api/modules/staff";
import { Dept } from "@/typings/dept";
import { getDeptList } from "@/api/modules/dept";
import { isEmpty } from "@/utils/is";

interface IShowInfo {
  label: string;
  value?: string;
  field: keyof Staff.Item;
}
const userStore = useUserStore();

const { job_grade } = useDict("job_grade");

const visible = ref<boolean>(false);

const postList = ref<Post.Item[]>([]);

const staffList = ref<Staff.Item[]>([]);

const deptList = ref<Dept.Item[]>([]);

const userInfo = computed(() => userStore.userInfo);

const postName = computed(() => postList.value.find(({ id }) => id === userInfo.value.postId)?.name || "暂无职位");

const rankName = computed(() => job_grade.value.find(({ value }) => value === userInfo.value.rank)?.label || "暂无职级");

const leaderName = computed(() => staffList.value.find(({ id }) => id === userInfo.value.leader)?.name || "暂无上级领导");

const deptInfo = computed(() => deptList.value.find(({ id }) => id === userInfo.value.deptId) || ({} as Dept.Item));
const deptParentInfo = computed(() => {
  if (isEmpty(deptInfo.value)) return {} as Dept.Item;
  return deptList.value.find(({ id }) => id === deptInfo.value.parentId) || ({} as Dept.Item);
});

const showInfo = computed<IShowInfo[]>(() => [
  { label: "姓名", field: "name" },
  { label: "工号", field: "jobNum" },
  { label: "职位", field: "postId", value: postName.value },
  { label: "职级", field: "rank", value: rankName.value },
  { label: "手机号码", field: "tel" },
  { label: "邮箱", field: "email" },
  { label: "直属领导", field: "leader", value: leaderName.value },
  { label: "部门", field: "deptId", value: deptInfo.value?.name ?? "暂无部门" },
  { label: "上级部门", field: "deptParent", value: deptParentInfo.value?.name ?? "暂无上级部门" }
]);

const _getDeptList = async () => {
  const { data } = await getDeptList();
  deptList.value = data;
};

const _getPostList = async () => {
  const {
    data: { list }
  } = await getPostList();
  postList.value = list;
};

const _getStaffList = async () => {
  const {
    data: { list }
  } = await getStaffList();
  staffList.value = list;
};

const setVisible = (val: boolean) => {
  visible.value = val;
};

onMounted(() => {
  // _getPostList();
  // _getStaffList();
  // _getDeptList();
});
defineExpose({ setVisible });
</script>
