import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { ProductionDataAnalysis } from "@/typings/productionDataAnalysis.d";
import { API_PREFIX } from "@/api/config/servicePort";
import { DimensionsItem } from "@/typings/productionData";

/**
 * OQA-MeasureData图表
 * @param params
 */
export const oqaMeasureData = (params: { product: string; supplier: string; parameter: string; model: string }) => {
  return http.post<ProductionDataAnalysis>(`${API_PREFIX}/productionDataAnalysis/oqaMeasure/getCapabilityData`, params);
};

/**
 * IPQA-MeasureData报表数据
 * @param params
 */
export const ipqaMeasureData = (params: { product: string; supplier: string; parameter: string; model: string }) => {
  return http.post<any>(`${API_PREFIX}/productionDataAnalysis/ipqaMeasure/getCapabilityData`, params);
};

/**
 * IPQA-Yield列表
 * @param params
 */
export const ipqaVmiDataList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionDataAnalysis/ipqaVmiData/yieldList`, params);
};

/**
 * IPQA-Yield图表
 * @param params
 */
export const ipqaVmiDataAnalysis = (params: ReqPage) => {
  return http.post<any>(`${API_PREFIX}/productionDataAnalysis/ipqaVmiData/yieldAnalysis`, params);
};
/**
 * IPQA-Pareto图表
 * @param params
 */
export const ipqaVmiDataParetoAnalysis = (params: ReqPage) => {
  return http.post<any>(`${API_PREFIX}/productionDataAnalysis/ipqaVmiData/paretoAnalysis`, params);
};

/**
 * IPQA-Yield列表
 * @param params
 */
export const ipqaVmiList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionData/ipqaVmi/list`, params);
};

/**
 * OQA-Lar图表
 * @param params
 */
export const oqaVmiDataAnalysis = (params: ReqPage) => {
  return http.post<any>(`${API_PREFIX}/productionDataAnalysis/oqaVmiData/larAnalysis`, params);
};
/**
 * OQA-Lar列表
 * @param params
 */
export const oqaVmiDataLarList = (params: ReqPage) => {
  return http.post<any>(`${API_PREFIX}/productionDataAnalysis/oqaVmiData/larList`, params);
};
/**
 * OQA-Pareto图表
 * @param params
 */
export const paretoAnalysis = (params: ReqPage) => {
  return http.post<any>(`${API_PREFIX}/productionDataAnalysis/oqaVmiData/paretoAnalysis`, params);
};
/**
 * OQA-Pareto列表
 * @param params
 */
export const oqaVmiDataParetoList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionDataAnalysis/oqaVmiData/paretoList`, params);
};

/**
 * IPQA-Yield 导出
 * @param params
 */
export const ipqaVmiDataYieldExport = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionDataAnalysis/ipqaVmiData/yieldExport`, params);
};

/**
 * OQA-Lar 导出
 * @param params
 */
export const oqaVmiDataLarExport = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionDataAnalysis/oqaVmiData/larExport`, params);
};

/**
 * OQA-Pareto 导出
 * @param params
 */
export const oqaVmiDataParetoExport = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionDataAnalysis/oqaVmiData/paretoExport`, params);
};
