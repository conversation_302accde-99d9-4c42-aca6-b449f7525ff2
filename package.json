{"name": "het", "private": true, "version": "1.2.0", "type": "module", "description": "quality-cost-front", "author": {"name": "<PERSON>ker", "email": "<EMAIL>", "url": "https://github.com/HalseySpicy"}, "license": "MIT", "homepage": "https://github.com/HalseySpicy/Geeker-Admin", "repository": {"type": "git", "url": "https://gitee.com/veloma-timer/meta-client-score-front.git"}, "bugs": {"url": "https://github.com/HalseySpicy/Geeker-Admin/issues"}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:pro": "vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue-flow/additional-components": "^1.3.3", "@vue-flow/core": "^1.26.0", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "deepcopy": "^2.1.0", "driver.js": "^1.3.0", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.3.4", "exceljs": "^4.4.0", "fingerprintjs2": "^2.1.4", "html2canvas": "^1.4.1", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "number-precision": "^1.6.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "plotly.js-dist-min": "^3.0.1", "print-js": "^1.6.0", "qs": "^6.11.2", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "vue": "^3.3.7", "vue-i18n": "^9.6.0", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^18.1.0", "@commitlint/config-conventional": "^18.1.0", "@types/crypto-js": "^4.2.1", "@types/fingerprintjs2": "^2.0.0", "@types/md5": "^2.3.4", "@types/nprogress": "^0.2.2", "@types/plotly.js-dist-min": "^2.3.4", "@types/qs": "^6.9.9", "@types/sortablejs": "^1.15.4", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "autoprefixer": "^10.4.16", "cz-git": "^1.7.1", "czg": "^1.7.1", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.18.0", "husky": "^8.0.3", "lint-staged": "^15.0.2", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.69.5", "standard-version": "^9.5.0", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.0.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "unplugin-vue-setup-extend-plus": "^1.0.0", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-pwa": "^0.16.5", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.4"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}