<script setup lang="ts" name="UserInfo">
import { computed } from "vue";
import useUserStore from "@/stores/modules/user";
// import UploadImg from "@/components/Upload/Img.vue";
// import { editStaff, updateAvatarFile } from "@/api/modules/staff";
import { getTimeState } from "@/utils";
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
import { useI18n } from "vue-i18n";
const { t } = useI18n();
// const updateAvatar = async (avatar: string) => {
//   // console.log(avatar);
//   const updateData = Object.assign({}, userInfo.value, { avatar });
//   await editStaff(updateData);
//   userStore.setUserInfo(updateData);
// };
</script>

<template>
  <div class="flex flex-shrink-0 gap-2 p-2 card">
    <!--    <UploadImg :api="updateAvatarFile" :image-url="userInfo.avatar" @update:image-url="updateAvatar">-->
    <!--      <template #empty>-->
    <!--        <span>{{ $t("请上传图片") }}</span>-->
    <!--      </template>-->
    <!--    </UploadImg>-->
    <div class="flex flex-col flex-1 gap-4">
      <div class="p-2 text-lg">{{ userInfo.nickName }}，{{ getTimeState(t) }}</div>
      <div class="flex gap-4">
        <div class="px-4 py-2 bg-[#009dff1a] border rounded border-[#009dff1a] text-[#009DFF]">
          {{ $t("工号") }}: {{ userInfo.jobNum }}
        </div>
        <div class="px-4 py-2 bg-[#009dff1a] border rounded border-[#009dff1a] text-[#009DFF]">
          {{ $t("手机号码") }}: {{ userInfo.phonenumber }}
        </div>
        <div class="px-4 py-2 bg-[#009dff1a] border rounded border-[#009dff1a] text-[#009DFF]">
          {{ $t("邮箱") }}: {{ userInfo.email }}
        </div>
      </div>
    </div>
  </div>
</template>
