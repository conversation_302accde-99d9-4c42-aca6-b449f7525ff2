import { ReqPage, ResPage } from "@/api/interface/index";
import { Staff } from "@/typings/staff";
import http from "@/api";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_staff`;
export const auditStaff = (params?: ReqPage) => {
  return http.get<ResPage<Staff.Item>>(`${baseUrl}/list`, params);
};

export const deleteStaffAudit = (params?: ReqPage) => {
  return http.get<ResPage<Staff.Item>>(`${baseUrl}/list`, params);
};

export const getStaffAuditList = (params?: ReqPage) => {
  return http.get<ResPage<Staff.Item>>(`${baseUrl}/list`, params);
};
