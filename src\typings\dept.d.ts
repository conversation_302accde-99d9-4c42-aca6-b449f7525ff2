export namespace Dept {
  export interface Item {
    disabled: boolean;
    label: any;
    value: any;
    deptId: any;
    deptName: any;
    id: number;
    parentId: number; // 上级部门id root为null
    parentName?: string; // 上级部门名称 可选
    name: string; // 部门名称
    leader: string; // 负责人名称
    tel: string; // 联系电话
    email: string; // 邮箱
    status: string; // 系统字典 common_status '0' -> 停用 '1' -> 正常
    createdTime: string;
    deptChain: string;
    remark?: any;
    sort: number; // 排序
    children: Item[];
  }
}
