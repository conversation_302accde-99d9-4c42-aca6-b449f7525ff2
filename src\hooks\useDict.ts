/**
 * 获取字典数据
 */

import { getDictDataList, getAdminDictDataList } from "@/api/modules/dict";
import useDictStore from "@/stores/modules/dict";
import { Dict } from "@/typings/dict";
import { ref, toRefs } from "vue";
import { isNumber } from "@/utils/is";

export function useDict(...args: string[]) {
  const res = ref<{ [key: string]: Dict.IDataItem[] }>({});
  return (() => {
    args.forEach(type => {
      res.value[type] = [];
      const dicts = useDictStore().getDict(type);
      if (dicts) {
        res.value[type] = dicts;
      } else {
        getDictDataList(type).then(resp => {
          const typesData = resp.data.map(item => {
            item.label = item.dictLabel;
            item.value = item.dictValue;
            item.type = item.dictType;
            return item;
          });
          res.value[type] = typesData;
          useDictStore().setDict(type, res.value[type]);
        });
      }
    });
    return toRefs(res.value);
  })();
}

export function useAdminDict(...args: string[]) {
  const res = ref<{ [key: string]: Dict.IDataItem[] }>({});
  return (() => {
    args.forEach(type => {
      res.value[type] = [];
      const dicts = useDictStore().getDict(type);
      if (dicts) {
        res.value[type] = dicts;
      } else {
        getAdminDictDataList({ type, status: "1" }).then(resp => {
          res.value[type] =
            type === "sys_yes_or_no" ? resp.data.map(v => ({ ...v, value: v.value === "Y" ? true : false })) : resp.data;
          useDictStore().setDict(type, res.value[type]);
        });
      }
    });
    return toRefs(res.value);
  })();
}
