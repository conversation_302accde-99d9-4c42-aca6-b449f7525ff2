<template>
  <div class="cursor-pointer" @click="toAdmin">
    <svg
      t="1703164096488"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="5986"
      width="20"
      height="20"
    >
      <path
        d="M263.1 844.8h497.7c22.9 0 41.5 18.6 41.5 41.5s-18.6 41.5-41.5 41.5H263.1c-22.9 0-41.5-18.6-41.5-41.5s18.6-41.5 41.5-41.5zM138.7 98.2h746.6c22.9 0 41.5 18.6 41.5 41.5v580.7c0 11-4.4 21.6-12.1 29.3-7.8 7.8-18.3 12.1-29.3 12.1H138.7c-11 0-21.6-4.4-29.3-12.1-7.8-7.8-12.1-18.3-12.1-29.3V139.7c-0.1-22.9 18.5-41.5 41.4-41.5z m331.8 478.9v60.4h83v-60.3c8.5-5.8 17.4-12.8 26.5-20.5l49.4 49.4 58.7-58.7-49.5-49.4c7.7-9 14.6-18 20.5-26.5h60.3v-83h-60.3c-6.4-9.1-13.3-18-20.5-26.5l49.4-49.4-58.7-58.6-49.4 49.4c-8.5-7.3-17.3-14.1-26.5-20.5v-60.3h-83V283c-8.5 5.8-17.4 12.8-26.5 20.5l-49.4-49.4-58.5 58.6 49.4 49.4c-7.2 8.5-14.1 17.3-20.5 26.5h-60.3v83h60.3c5.8 8.5 12.8 17.4 20.5 26.5L336 547.3l58.7 58.7 49.4-49.4c9.1 7.7 18 14.6 26.4 20.5z m0 0"
        p-id="5987"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { getAppList } from "@/api/modules/app";
import { App } from "@/typings/app";
import { isEmpty } from "@/utils/is";
import { localSet } from "@/utils";
import useUserStore from "@/stores/modules/user";
import { ADMIN_APP_CODE } from "@/config";

const appList = ref<App.Item[]>([]);

const url = computed(() => {
  return appList.value.find(({ code }) => code === ADMIN_APP_CODE)?.url || "";
});

const userStore = useUserStore();

const _getAppList = async () => {
  const {
    data: { list }
  } = await getAppList();
  appList.value = list;
};

const toAdmin = () => {
  if (isEmpty(url.value)) return;
  window.open(url.value + "?token=" + userStore.token);
};

onMounted(() => {
  _getAppList();
});
</script>
