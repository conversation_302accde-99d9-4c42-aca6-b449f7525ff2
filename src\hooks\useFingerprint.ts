import { onMounted } from "vue";
import useGlobalStore from "@/stores/modules/global";
import { generateUUID, localGet, localSet } from "@/utils";
import { isEmpty } from "@/utils/is";

export const useFingerprint = () => {
  onMounted(() => {
    createFingerprint();
  });
};

export function createFingerprint() {
  return new Promise((resolve, reject) => {
    let fingerprint = localGet("fingerprint") || "";
    if (isEmpty(fingerprint)) {
      fingerprint = generateUUID();
      localSet("fingerprint", fingerprint);
    }
    useGlobalStore().setGlobalState("fingerprint", fingerprint);
    resolve(fingerprint);
  });
}
