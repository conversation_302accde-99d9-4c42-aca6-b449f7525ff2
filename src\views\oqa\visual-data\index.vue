<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!--       表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'production:oqa:vmi:import'" type="primary" @click="handleImport">Import</el-button>
        <el-button v-auth="'production:oqa:vmi:export'" type="primary" @click="handleExport">Export</el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button v-auth="'production:oqa:vmi:edit'" type="primary" link @click="handleModalOperation('Edit', row)">
          Edit
        </el-button>
        <el-button v-auth="'production:oqa:vmi:del'" type="danger" link @click="handleRemove(row)">Del</el-button>
      </template>
    </ProTable>
    <ImportExcel ref="ImportExcelRef" />

    <handleModal ref="handleModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { ref, reactive, onMounted, nextTick } from "vue";

import { Role } from "@/typings/role";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";

import {
  oqaMeasureDel,
  oqaMeasureEdit,
  oqaMeasureExport,
  oqaMeasureExportTmpl,
  oqaMeasureList,
  oqaVmiDel,
  oqaVmiEdit,
  oqaVmiExport,
  oqaVmiExportTmpl,
  oqaVmiImport,
  visualdataList
} from "@/api/modules/productionData";
import ImportExcel from "@/components/ImportExcel/index.vue";
import DateRange from "@/views/components/DateRange.vue";
import { DimensionsItem, DimensionsQuery } from "@/typings/productionData";
import { isEmpty } from "@/utils/is";
import { useDict } from "@/hooks/useDict";
import { downloadFileByName, downloadTemplateFie, downloadTemplateSourceFile } from "@/utils/download";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
import { formatParams } from "@/utils/util";
import { ExceptionManageItem } from "@/typings/exception-manage";
import { useHandleData } from "@/hooks/useHandleData";

import handleModal from "@/views/oqa/visual-data/components/handleModal.vue";
import { getOperationColWidth, visibleOperationCol } from "@/utils";
import { useAuthStore } from "@/stores/modules/auth";
import { useI18n } from "vue-i18n";
const handleModalRef = ref<InstanceType<typeof handleModal>>();
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>();

const proTable = ref<ProTableInstance>();
const supplierList = ref<Supplier.Item[]>([]);

const { check, currentRow } = useCheckSelectId();
const { product_names, production_model } = useDict("product_names", "production_model");
let initParam = reactive({
  pageSize: 10,
  pageNum: 1
});
const { t } = useI18n();
const auth = useAuthStore();
const pageButtons = ["production:oqa:vmi:edit", "production:oqa:vmi:del"];
const columns = reactive<ColumnProps<Role.Item>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  { type: "index", label: "NO", width: 70 },
  {
    prop: "date",
    label: "Date",
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "supplier",
    label: "Supplier",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "product",
    label: "Product",
    enum: product_names,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.product} placeholder={t("请选择")} clearable>
            {product_names.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "model",
    label: "Model",
    enum: production_model,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.model} placeholder={t("请选择")} clearable>
            {production_model.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "doNo", label: "DO", search: { el: "input" } },
  { prop: "lotNo", label: "Lot No", search: { el: "input" } },
  { prop: "weekCode", label: "WeekCode", search: { el: "input" } },
  { prop: "defectCategory", label: "Defect Category" },
  { prop: "defectQty", label: "Defect Qty" },
  { prop: "createBy", label: "Creator" },
  { prop: "createTime", label: "Create Time" },
  ...(visibleOperationCol(auth.authButtonList, pageButtons)
    ? [
        {
          prop: "operation",
          label: t("操作"),
          width: getOperationColWidth(auth.authButtonList, pageButtons),
          fixed: "right"
        }
      ]
    : [])
]);
let queryParams = reactive<DimensionsQuery.IQueryParams>({} as DimensionsQuery.IQueryParams);

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  // const { pageNum, pageSize, ...condition } = params;
  //
  // if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
  //   condition.startDate = filterDate.value[0];
  //   condition.endDate = filterDate.value[1];
  // }
  //
  // queryParams = reactive(condition);
  const result = formatParams(initParam, params, filterDate.value);
  // initParam.pageNum = result.pageNum;
  // initParam.pageSize = result.pageSize;
  initParam = result;
  return visualdataList(result);
};

/**
 * 批量导入
 */
const handleImport = () => {
  const params = {
    title: "OQA Dimensions",
    importApi: oqaVmiImport,
    getTableList: proTable.value?.getTableList,
    tempFun: downloadTemplateSourceFile,
    tempApi: oqaVmiExportTmpl
  };
  ImportExcelRef.value?.acceptParams(params);
};

const handleExport = async () => {
  await downloadFileByName(oqaVmiExport, initParam.condition);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};

const handleModalOperation = (title: string, row?: ExceptionManageItem) => {
  const form = isEmpty(row?.id) ? {} : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    api: oqaVmiEdit,
    getTableList: proTable.value?.getTableList
  };
  handleModalRef.value?.acceptParams(params);
};

/**
 * 删除
 * @param row
 */
const handleRemove = async (row: DimensionsItem) => {
  await useHandleData(oqaVmiDel, { ids: [row.id] }, `Delete selected data`);
  proTable.value?.getTableList();
};
onMounted(async () => {
  await getSupplierData();
});
</script>
