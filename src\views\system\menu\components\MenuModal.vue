<template>
  <el-dialog v-model="visible" width="40%" :destroy-on-close="true" :title="$t(`${$t(title)}${typeName}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :validate-on-rule-change="false"
    >
      <el-row>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item :label="$t('绑定应用')" prop="appCode">-->
        <!--            <el-select v-model="form.appCode">-->
        <!--              <el-option v-for="{ name, code } of appList" :key="code" :label="$t(name)" :value="code" />-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col v-if="!isEmpty(form.parentId)">
          <el-form-item :label="$t('上级菜单')" prop="parentId">
            <el-tree-select
              v-model="form.parentId"
              :data="menuList"
              :props="{ value: 'menuId', label: 'menuName', children: 'children' }"
              :placeholder="$t('选择上级菜单')"
              value-key="id"
              check-strictly
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('类型')" prop="menuType">
            <el-radio-group v-model="form.menuType">
              <el-radio v-for="{ label, value } of typeOptions" :label="value" :key="value">{{ $t(label) }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t(typeName + $t('图标'))" prop="icon">
            <SelectIcon v-model:icon-value="form.icon" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t(typeName + $t('名称'))" prop="menuName">
            <el-input v-model="form.menuName" :placeholder="$t(`请填写`)" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('显示顺序')" prop="orderNum">
            <el-input v-model="form.orderNum" type="number" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12" v-if="form.type === 'M'">-->
        <!--          <el-form-item :label="$t('绑定流程')" prop="workflowId">-->
        <!--            <el-select v-model="form.workflowId">-->
        <!--              <el-option v-for="{ name, id } of workflowList" :key="id" :label="$t(name)" :value="id" />-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->

        <el-col :span="12" v-if="form.menuType === 'M'">
          <el-form-item prop="routeName">
            <template #label>
              <span>
                <el-tooltip :content="$t('访问的路由名称，如：`user`')" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("路由名称") }}
              </span>
            </template>
            <el-input v-model="form.routeName" :placeholder="$t('请输入')" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType === 'M' || form.menuType === 'C'">
          <el-form-item prop="path">
            <template #label>
              <span>
                <el-tooltip
                  :content="$t('访问的路由地址，如：`/user`，如外网地址需内链访问则以`http(s)://`开头')"
                  placement="top"
                >
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("路由地址") }}
              </span>
            </template>
            <el-input v-model="form.path" :placeholder="$t('请输入')" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType === 'M'">
          <el-form-item prop="component">
            <template #label>
              <span>
                <el-tooltip :content="$t('访问的组件路径，如：`/user/index`，默认在`views`目录下')" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("组件路径") }}
              </span>
            </template>
            <el-input v-model="form.component" :placeholder="$t('请输入')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <el-input v-model="form.perms" :placeholder="$t('请输入')" maxlength="100" />
            <template #label>
              <span>
                <el-tooltip :content="$t('鉴权时用到的菜单或者按钮标识，如：`user:list`，`user:add`')" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("权限字符") }}
              </span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType === 'M' || form.menuType === 'C'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip :content="$t('选择是外链则路由地址需要以`http(s)://`开头')" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("是否外链") }}
              </span>
            </template>
            <el-radio-group v-model="form.isFrame">
              <el-radio v-for="(item, index) in sys_yes_no" :key="index" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType === 'M'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip :content="`选择是则会被keep-alive缓存，需要匹配组件的name和地址保持一致`" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("是否缓存") }}
              </span>
            </template>
            <el-radio-group v-model="form.isCache">
              <el-radio v-for="(item, index) in sys_yes_no" :key="index" :value="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.menuType === 'M' || form.menuType === 'C'">
          <el-form-item>
            <template #label>
              <span>
                <el-tooltip :content="$t('选择隐藏则路由将不会出现在侧边栏，但仍然可以访问')" placement="top">
                  <el-icon><question-filled /></el-icon>
                </el-tooltip>
                {{ $t("是否隐藏") }}
              </span>
            </template>
            <el-radio-group v-model="form.visible">
              <el-radio v-for="({ label, value }, index) of sys_show_hide" :key="index" :label="value">{{ $t(label) }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('状态')" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{ $t(label) }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="MenuModal">
import { ElMessage, FormInstance } from "element-plus";
import { ref, toRefs, reactive, computed } from "vue";
import { Menu, MenuItem } from "@/typings/menu";
import { getMenuList } from "@/api/modules/menu";
import SelectIcon from "@/components/SelectIcon/index.vue";
import { isEmpty } from "@/utils/is";
import { Dict } from "@/typings/dict";
import { useDict } from "@/hooks/useDict";
// import { WorkFlow } from "@/typings/work-flow";
// import { getWorkFlowList } from "@/api/modules/work-flow";
import { App } from "@/typings/app";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { StatusTypes } from "@/utils/dicts/type";
import { ResultData } from "@/api/interface";
// import { common_yes_no } from "@/utils/dicts";
const { sys_yes_no, sys_show_hide } = useDict("sys_yes_no", "sys_show_hide");
import { getMessage, handleTree } from "@/utils";
interface IState {
  title: string;
  isView: boolean;
  form: Partial<MenuItem>;
  common_status: Dict.IDataItem[];
  appList: App.Item[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const typeOptions = [
  // { label: "目录", value: "C" },
  { label: getMessage("菜单"), value: "M" },
  { label: getMessage("按钮"), value: "F" }
];

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "180px"));

// const { common_yes_no } = useDict("common_yes_no");
// const common_yes_no = ref([]);
const workflowList = ref<any[]>([]);
const menuList = ref<MenuItem>();
const visible = ref(false);

const formRef = ref<FormInstance>();
const { t } = useI18n();
const state = reactive<IState>({
  isView: false,
  title: "",
  common_status: [],
  appList: [],
  form: {}
});

const { form, title, isView, common_status, appList } = toRefs(state);

const typeName = computed(() => typeOptions.find(item => item.value === form.value.menuType)?.label);

const rules = computed(() => ({
  menuName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  // appCode: [{ required: true, message: `请选择应用名称`, trigger: "change" }],
  path: [{ required: true, message: t("请填写"), trigger: "blur" }],
  name: [{ required: true, message: t("请填写"), trigger: "blur" }],
  perms: [{ required: true, message: t("请填写"), trigger: "blur" }]
}));

const setVisible = (val: boolean) => {
  visible.value = val;
};

// const _getWorkFlowList = async () => {
// const {
//   data: { list }
// } = await getWorkFlowList();
// workflowList.value = list;
// };

const getMenuAllList = async () => {
  let { data } = await getMenuList();
  menuList.value = handleTree(data, "menuId", "parentId", "children", true, "orderNum");
};

// 接收父组件传过来的参数
// isCache;
// isFrame;
//
// menuName: menuType;
// orderNum;
// parentId;
// path;
// status;
// visible;

const acceptParams = async (params: {
  form: {
    redirect?: string;
    isFrame?: number | string;
    icon?: string;
    isAffix?: boolean;
    visible?: number | string;
    orderNum?: number;
    title?: string;
    menuType?: string;
    parentId?: number;
    isHide?: string;
    path?: string;
    parentName?: string;
    component?: string;
    children?: Menu.Item[];
    isCache: number | string;
    menuName?: string;
    menuId?: number;
    createdTime?: string;
    perms?: string;
    id?: number;
    isFull?: boolean;
    workflowId?: number;
    status?: string;
    routeName?: string;
  };
  common_status: StatusTypes[];
  isView: boolean;
  getTableList: (() => Promise<void>) | undefined;
  // appList: Array<UnwrapRefSimple<App.Item>>;
  api: (data: Menu.Item) => Promise<ResultData<unknown>>;
  title: string;
}) => {
  await getMenuAllList();
  // _getWorkFlowList();
  if (isEmpty(params.form.parentId)) {
    params.form.parentId = 0;
  }

  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      if (!form.value.icon) form.value.icon = "#";
      await state.api!(form.value);
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
