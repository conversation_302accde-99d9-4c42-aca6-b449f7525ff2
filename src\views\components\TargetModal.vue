<template>
  <el-dialog v-model="visible" width="80%" :destroy-on-close="true" :title="$t('设置目标值')">
    <div class="container" id="target-items">
      <el-row>
        <el-col :span="4">
          <el-button type="primary" @click="showYearDialog">{{ $t("新增年份") }}</el-button>
          <el-menu :default-active="defaultActive" :default-openeds="defaultOpeneds">
            <el-sub-menu v-for="(item, index) in menuTreeList" :key="index" :index="index + 1">
              <template #title>
                <span>{{ item.name }}</span>
              </template>
              <el-menu-item
                v-for="(subItem, subIndex) in item.children"
                :key="subIndex"
                :index="`${index + 1}-${subIndex + 1}`"
                @click="setTarget(subItem)"
              >
                {{ $t(subItem.name) }}
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-col>

        <el-col :span="20">
          <div class="header">
            <span style="margin-right: 20px">
              {{ $t("工厂") }}: <b>{{ t(currFactory.name ?? "") }}</b>
            </span>

            <span>
              {{ $t("年份") }}: <b>{{ currFactory.parentName }}</b>
            </span>
          </div>

          <!-- 年度目标 -->
          <div class="target-section">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick" type="card">
              <el-tab-pane :label="$t('年度目标')" name="annual" v-if="types.includes('annual')">
                <el-table :data="targetListArr" style="width: 100%">
                  <el-table-column type="index" :label="$t('序号')" width="80" />
                  <el-table-column prop="statsTime" :label="$t('年度')"> </el-table-column>
                  <el-table-column prop="target" :label="$t('目标值')" v-slot="scope">
                    <el-input v-model="scope.row.target" type="number"><template #append v-if="isPercent">%</template></el-input>
                  </el-table-column>
                  <el-table-column :label="$t('操作')">
                    <template #default="scope">
                      <el-button type="text" size="small" @click="handleUpdate(scope.row)">{{ $t("保存") }}</el-button>
                      <el-button type="text" size="small" @click="handleDelete(scope.row.id)">{{ $t("删除") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" class="add-target" @click="showItemDialog()">{{ $t("新建目标值") }}</el-button>
              </el-tab-pane>

              <!-- 季度目标 -->
              <el-tab-pane :label="$t('季度目标')" name="quarterly" v-if="types.includes('quarterly')">
                <el-table :data="targetListArr" style="width: 100%">
                  <el-table-column type="index" :label="$t('序号')" width="80" />
                  <el-table-column prop="statsTime" :label="$t('季度')"> </el-table-column>
                  <el-table-column prop="target" :label="$t('目标值')" v-slot="scope">
                    <el-input v-model="scope.row.target" type="number"><template #append v-if="isPercent">%</template></el-input>
                  </el-table-column>
                  <el-table-column :label="$t('操作')">
                    <template #default="scope">
                      <el-button type="text" size="small" @click="handleUpdate(scope.row)">{{ $t("保存") }}</el-button>
                      <el-button type="text" size="small" @click="handleDelete(scope.row.id)">{{ $t("删除") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" class="add-target" @click="showItemDialog()">{{ $t("新建目标值") }}</el-button>
              </el-tab-pane>

              <!-- 月度目标 -->
              <el-tab-pane :label="$t('月度目标')" name="monthly" v-if="types.includes('monthly')">
                <el-table :data="targetListArr" style="width: 100%">
                  <el-table-column type="index" :label="$t('序号')" width="80" />
                  <el-table-column prop="statsTime" :label="$t('月份')"> </el-table-column>
                  <el-table-column prop="target" :label="$t('目标值')" v-slot="scope">
                    <el-input v-model="scope.row.target" type="number"><template #append v-if="isPercent">%</template></el-input>
                  </el-table-column>
                  <el-table-column :label="$t('操作')">
                    <template #default="scope">
                      <el-button type="text" size="small" @click="handleUpdate(scope.row)">{{ $t("保存") }}</el-button>
                      <el-button type="text" size="small" @click="handleDelete(scope.row.id)">{{ $t("删除") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" class="add-target" @click="showItemDialog()">{{ $t("新建目标值") }}</el-button>
              </el-tab-pane>

              <el-tab-pane :label="$t('周目标')" name="weekly" v-if="types.includes('weekly')">
                <el-table :data="targetListArr" style="width: 100%">
                  <el-table-column type="index" :label="$t('序号')" width="80" />
                  <el-table-column prop="statsTime" :label="$t('周')"> </el-table-column>
                  <el-table-column prop="target" :label="$t('目标值')" v-slot="scope">
                    <el-input v-model="scope.row.target" type="number"><template #append v-if="isPercent">%</template></el-input>
                  </el-table-column>
                  <el-table-column :label="$t('操作')">
                    <template #default="scope">
                      <el-button type="text" size="small" @click="handleUpdate(scope.row)">{{ $t("保存") }}</el-button>
                      <el-button type="text" size="small" @click="handleDelete(scope.row.id)">{{ $t("删除") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" class="add-target" @click="showItemDialog()">{{ $t("新建目标值") }}</el-button>
              </el-tab-pane>

              <!-- 添加日目标 -->
              <el-tab-pane :label="$t('日目标')" name="daily" v-if="types.includes('daily')">
                <el-table :data="targetListArr" style="width: 100%">
                  <el-table-column type="index" :label="$t('序号')" width="80" />
                  <el-table-column prop="statsTime" :label="$t('日期')"> </el-table-column>
                  <el-table-column prop="target" :label="$t('目标值')" v-slot="scope">
                    <el-input v-model="scope.row.target" type="number" />
                  </el-table-column>
                  <el-table-column :label="$t('操作')">
                    <template #default="scope">
                      <el-button type="text" size="small" @click="handleUpdate(scope.row)">{{ $t("保存") }}</el-button>
                      <el-button type="text" size="small" @click="handleDelete(scope.row.id)">{{ $t("删除") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" class="add-target" @click="showItemDialog()">{{ $t("新建目标值") }}</el-button>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("关闭") }}</el-button>
    </template>
  </el-dialog>

  <el-dialog v-model="yearDialogVisible" width="400px" :destroy-on-close="true" :title="$t('新增年份')">
    <el-form>
      <el-form-item :label="$t('年份')">
        <el-select v-model="newYear" :placeholder="$t('请选择年份')">
          <el-option v-for="item in yearList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addYear">{{ $t("确定") }}</el-button>
        <el-button @click="yearDialogVisible = false">{{ $t("取消") }}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>

  <el-dialog v-model="itemDialogVisible" width="400px" :destroy-on-close="true" :title="$t('添加目标值')">
    <el-form label-width="80">
      <el-form-item :label="$t('年份')" v-if="statsType === YEAR">
        <el-select v-model="statsTime" :placeholder="$t('选择年度')">
          <el-option
            v-for="item in [currFactory.parentName]"
            :key="item"
            :label="item"
            :value="item"
            :disabled="isDisabled(item)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('季度')" v-if="statsType === QUARTER">
        <el-select v-model="statsTime" :placeholder="$t('选择季度')">
          <el-option v-for="item in quarterOptions" :key="item" :label="item" :value="item" :disabled="isDisabled(item)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('月份')" v-if="statsType === MONTH">
        <el-select v-model="statsTime" :placeholder="$t('选择月份')">
          <el-option v-for="item in monthOptions" :key="item" :label="item" :value="item" :disabled="isDisabled(item)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('周')" v-if="statsType === WEEK">
        <el-date-picker
          v-model="statsTimeTemp"
          :placeholder="$t('选择周')"
          style="width: 100%"
          type="week"
          @change="changeStatsTime"
          format="YYYY-ww"
        />
      </el-form-item>
      <el-form-item :label="$t('日')" v-if="statsType === DAY">
        <el-date-picker
          v-model="statsTime"
          type="date"
          :placeholder="$t('选择日期')"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item :label="$t('目标值')">
        <el-input v-model="target" type="number">
          <template #append v-if="isPercent">%</template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addTarget">{{ $t("确定") }}</el-button>
        <el-button @click="yearDialogVisible = false">{{ $t("取消") }}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="tsx" name="TargetFunctionModal">
import { Target } from "@/typings/target";
import { ElMessage } from "element-plus";
import { ref, computed, watch, reactive, toRefs } from "vue";
import { useI18n } from "vue-i18n";
import { isEmpty } from "@/utils/is";
import { formatDateToISOWeek } from "@/utils";

interface IState {
  menuTreeSelect: () => Promise<any>;
  menuCreate: (params: any) => Promise<any>;
  menuDel: (params: any) => Promise<any>;
  targetCreate: (params: any) => Promise<any>;
  targetModify: (params: any) => Promise<any>;
  targetList: (params: any) => Promise<any>;
  targetDel: (params: any) => Promise<any>;
  isPercent: boolean;
  types: string[];
}
const state = reactive<IState>({ isPercent: false, types: [] as any } as IState);
const { isPercent, types } = toRefs(state);

const { t } = useI18n();

const visible = ref(false);
const yearDialogVisible = ref(false);
const newYear = ref("");
const showYearDialog = () => {
  yearDialogVisible.value = true;
};
const addYear = () => {
  if (isEmpty(newYear.value)) {
    ElMessage.error({ message: t("请选择年份") });
    return false;
  }
  _menuCreate();
};

const yearList = computed(() => {
  const currentYear = new Date().getFullYear();
  const yearList = [];
  for (let i = 0; i < 10; i++) {
    yearList.push(currentYear - i);
  }
  return yearList;
});

const lastMenuIndex = computed(() => menuTreeList.value.length);
const defaultOpeneds = computed(() => [lastMenuIndex.value.toString()]);
const defaultActive = computed(() => {
  return `${lastMenuIndex.value}-1`;
});

const changeStatsTime = (e: any) => {
  statsTime.value = formatDateToISOWeek(e);
};

const statsTimeTemp = ref("");
const statsTime = ref("");
const target = ref("");
const itemDialogVisible = ref(false);
const showItemDialog = () => {
  itemDialogVisible.value = true;
};
const addTarget = () => {
  if (isEmpty(statsTime.value) || isEmpty(target.value)) {
    ElMessage.error({ message: t("请填写完整信息") });
    return false;
  }
  _targetCreate();
};

const handleUpdate = (row: Target.target) => {
  if (!isEmpty(row.target)) {
    if (isPercent.value) {
      if (parseFloat(row.target) < 0 || parseFloat(row.target) > 100) {
        return ElMessage.error({ message: t("请输入正确的百分比0-100") });
      }
    } else {
      if (parseFloat(row.target) < 0) {
        return ElMessage.error({ message: t("请输入不小于0的数值") });
      }
    }
    _targetModify(row);
  }
};

const _menuCreate = async () => {
  const { success } = await state.menuCreate({
    statsYear: newYear.value
  });
  if (success) {
    _menuTreeSelect();
    yearDialogVisible.value = false;
    newYear.value = "";
  }
};

const _targetModify = async (row: Target.target) => {
  const { success } = await state.targetModify(row);
  if (success) {
    ElMessage.success({ message: t("修改成功") });
    _targetList();
  } else {
    ElMessage.error({ message: t("修改失败") });
  }
};

const _targetCreate = async () => {
  if (isPercent.value) {
    if (parseFloat(target.value) < 0 || parseFloat(target.value) > 100) {
      return ElMessage.error({ message: t("请输入正确的百分比0-100") });
    }
  } else {
    if (parseFloat(target.value) < 0) {
      return ElMessage.error({ message: t("请输入不小于0的数值") });
    }
  }
  const { success } = await state.targetCreate({
    plant: currFactory.value.name,
    statsType: statsType.value,
    statsTime: statsTime.value,
    target: target.value
  });
  if (success) {
    _targetList();
    itemDialogVisible.value = false;
    statsTime.value = "";
    target.value = "";
    statsTimeTemp.value = "";
  }
};

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  if (params.types && params.types.length > 0) {
    activeTab.value = params.types[0];
    setStatsType(params.types[0]);
  }
  setVisible(true);
};

const activeTab = ref("annual");
const quarterOptions = computed(() => {
  return ["Q1", "Q2", "Q3", "Q4"].map(v => currFactory.value.parentName + "-" + v);
});
const monthOptions = computed(() => {
  return ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"].map(
    v => currFactory.value.parentName + "-" + v
  );
});

const YEAR = 4;
const QUARTER = 3;
const MONTH = 2;
const WEEK = 1;
const DAY = 0;

const setStatsType = (name: string) => {
  if (name === "annual") {
    statsType.value = YEAR;
  } else if (name === "quarterly") {
    statsType.value = QUARTER;
  } else if (name === "monthly") {
    statsType.value = MONTH;
  } else if (name === "weekly") {
    statsType.value = WEEK;
  } else if (name === "daily") {
    statsType.value = DAY;
  }
};

const handleTabClick = (tab: any) => {
  setStatsType(tab.paneName);
  _targetList();
};

const isDisabled = (date: string) => {
  return targetListArr.value.some(target => target.statsTime === date);
};

const currFactory = ref<Target.menuChild>({} as Target.menuChild);
const setTarget = (item: Target.menuChild) => {
  currFactory.value = item;
  _targetList();
};
const menuTreeList = ref<Target.menu[]>([]);
const targetListArr = ref<Target.target[]>([]);
const statsType = ref(4);

const handleDelete = (id: number) => {
  _targetDel(id);
};

const _menuTreeSelect = async () => {
  const { data, success } = await state.menuTreeSelect();
  if (success) {
    menuTreeList.value = data;
    currFactory.value = data[data.length - 1]["children"][0];
    _targetList();
  }
};

const _targetDel = async (id: number) => {
  const { success } = await state.targetDel([id]);
  if (success) {
    targetListArr.value = targetListArr.value.filter(item => item.id !== id);
  }
};
const _targetList = async () => {
  if (isEmpty(currFactory.value.parentName)) {
    return ElMessage.error({ message: t("请选择年份") });
  }
  const {
    data: { list },
    success
  } = await state.targetList({
    condition: {
      statsYear: currFactory.value.parentName,
      plant: currFactory.value.name,
      statsType: statsType.value
    },
    pageNum: 1,
    pageSize: 1000
  });
  if (success) {
    targetListArr.value = list;
  }
};

watch(
  () => visible.value,
  newVal => {
    if (newVal) {
      _menuTreeSelect();
    }
  },
  { deep: true }
);

defineExpose({
  acceptParams
});
</script>

<style>
#target-items.container {
  display: flex;
  flex-direction: column;
  padding: 20px;
}
#target-items .header {
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
  margin-bottom: 20px;
}
#target-items .target-section {
  padding-left: 20px;
  margin-bottom: 40px;
}
#target-items .el-menu-item {
  height: 45px;
}
#target-items .el-sub-menu__title {
  height: 40px;
}
#target-items .el-sub-menu .el-menu-item {
  height: 35px;
}
#target-items .table-box .el-table .el-table__body-wrapper,
#target-items .table-main .el-table .el-table__body-wrapper {
  min-height: none;
}
#target-items .add-target {
  margin-top: 10px;
}
#target-items .el-dialog__body {
  padding-top: 20px;
}
</style>
