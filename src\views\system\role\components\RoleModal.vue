<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form ref="formRef" label-width="110px" label-suffix=" :" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row>
        <el-col :span="20">
          <el-col :span="12">
            <el-form-item :label="$t('角色名称')" prop="roleName">
              <el-input v-model="form.roleName" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('权限字符')" prop="roleKey">
              <el-input v-model="form.roleKey" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('说明')" prop="remark">
              <el-input v-model="form.remark" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('权限')" prop="remark">
              <!--              <el-tree-->
              <!--                :props="{ label: 'label', children: 'children' }"-->
              <!--                :data="menuList"-->
              <!--                :empty-text="$t('加载中,请稍候')"-->
              <!--                ref="treeSelectRef"-->
              <!--                check-strictly-->
              <!--                show-checkbox-->
              <!--                node-key="id"-->
              <!--                class="tree-border max-h-[400px] overflow-auto"-->
              <!--              >-->
              <!--                <template #default="{ node }">-->
              <!--                  <span class="custom-tree-node">-->
              <!--                    {{ node.label }}-->
              <!--                  </span>-->
              <!--                </template>-->
              <!--              </el-tree>-->
              <el-tree
                class="tree-border"
                :data="menuList"
                show-checkbox
                ref="treeSelectRef"
                node-key="id"
                :check-strictly="false"
                :props="{ label: 'label', children: 'children' }"
              >
                <template #default="{ node }">
                  <div class="custom-tree-node">
                    <span>{{ $t(node.label) }}</span>
                  </div>
                </template>
              </el-tree>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('状态')" prop="address">
              <el-radio-group v-model="form.status">
                <el-radio v-for="(item, index) in common_status" :key="index" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="RoleModal">
import { ElMessage, FormInstance, ElTreeSelect } from "element-plus";
import { getRoleDetail, roleMenuTreeselect } from "@/api/modules/role";
import { getMenuList, getMenuTree } from "@/api/modules/menu";
import { ref, reactive, toRefs, nextTick } from "vue";
import { Role } from "@/typings/role";
import { Dict } from "@/typings/dict";
import { Menu } from "@/typings/menu";
import { handleTree } from "@/utils";
import { isEmpty } from "@/utils/is";
import { useI18n } from "vue-i18n";
import { useDict } from "@/hooks/useDict";

const { common_status } = useDict("common_status");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Role.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const { t } = useI18n();

const rules = {
  roleName: [{ required: true, message: "请填写角色名称", trigger: "blur" }],
  roleKey: [{ required: true, message: "请填权限字符", trigger: "blur" }]
};

const treeSelectRef = ref<InstanceType<typeof ElTreeSelect> | null>();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  // common_status: [],
  form: {}
});

const menuList = ref<Menu.Item[]>([]);

const { form, title, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

const getRole = async (id: number) => {
  const { data } = await getRoleDetail(id);
  form.value = data;
  Object.assign(form.value, { ...form.value, ...data });
};
const getRoleMenuTreeSelect = async (id: number) => {
  const { checkedKeys } = await roleMenuTreeselect(id);

  form.value.permissions = checkedKeys;
};
const getAllMenuList = async () => {
  const { data } = await getMenuTree();
  menuList.value = data;
};

// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  await getAllMenuList();
  if (!isEmpty(params.form.roleId)) {
    await getRole(params.form!.roleId!);
    await getRoleMenuTreeSelect(params.form!.roleId!);
    // await nextTick(() => {
    //   form.value.permissions.forEach(item => {
    //     console.log(item);
    //     treeSelectRef.value?.setChecked(item, true, false);
    //   });
    // });
  }

  setVisible(true);

  await nextTick(() => {
    console.log(form.value.permissions);
    treeSelectRef.value?.setCheckedKeys(form.value.permissions, false);
  });
};

/** 所有菜单节点数据 */
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  let checkedKeys = treeSelectRef.value!.getCheckedKeys();
  // 半选中的菜单节点
  let halfCheckedKeys = treeSelectRef.value!.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  console.log([...new Set(checkedKeys)]);
  return [...new Set(checkedKeys)];
}
// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const checkedKeys: number[] = getMenuAllCheckedKeys();
      // const halfCheckedKeys: number[] = treeSelectRef.value!.getHalfCheckedKeys();
      // console.log(treeSelectRef.value!.getCheckedNodes());
      //
      // console.log(checkedKeys);
      // console.log(halfCheckedKeys);
      // const checkedAllKeys = checkedKeys.concat(halfCheckedKeys);
      await state.api!({
        ...form.value,
        permissions: null,
        roleSort: 1,
        menuIds: checkedKeys,
        deptCheckStrictly: true,
        deptIds: [],
        menuCheckStrictly: true
      });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style>
.tree-border {
  width: 100%;
  margin-top: 5px;
  background: #ffffff none;
  border: 1px solid #e5e6e7;
  border-radius: 4px;
}
</style>
