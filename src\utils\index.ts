import { isArray, isEmpty, isNumber } from "@/utils/is";
import { FieldNamesProps } from "@/components/ProTable/interface";
import { Menu, MenuItem } from "@/typings/menu";
import useLanguageStore from "@/stores/modules/language";
import CryptoJS from "crypto-js";
import { getAppDetail } from "@/api/modules/app";
import { ElMessage } from "element-plus";
import { Staff } from "@/typings/staff";

// import { Statistics } from "@/typings/customer-refund/statistics";

const mode = import.meta.env.VITE_ROUTER_MODE;

const PRIVATE_KEY = import.meta.env.VITE_PRIVATE_KEY;

interface TreeNode<T> {
  [key: string]: any; // 允许 TreeNode 包含任意属性
}

/**
 * 高效查找函数
 * @param dataArray 数据数组
 * @param field 字段名
 * @param target 目标值
 * @returns 匹配的项，如果没有匹配项则返回 undefined
 */
export function efficientSearch<T>(dataArray: T[], field: keyof T, target: any): T | undefined {
  if (isEmpty(target)) return undefined;
  const index: { [key: string]: T } = {};
  // 构建哈希表索引
  for (const item of dataArray) {
    const key = item[field];
    if (!index[key as string]) {
      index[key as string] = item;
    }
  }

  return index[target];
}

//格式化名称
export function convertFileName(originalName: any) {
  const ts = parseTime(new Date(), "{y}{m}{d}{h}{i}{s}");
  const dotIndex = originalName.lastIndexOf(".");
  if (dotIndex === -1) {
    return `${originalName}${ts}`;
  }
  const name = originalName.substring(0, dotIndex);
  const extension = originalName.substring(dotIndex);
  return `${name}${ts}${extension}`;
}

//下载文件
export async function downloadFile(url: any, name: any) {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    const blob = await response.blob();
    const downloadUrl = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = downloadUrl;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(downloadUrl); // 释放URL对象
  } catch (error) {
    console.error("Download failed", error);
  }
}

/**
 * 日期格式化
 * @param time
 * @param pattern
 * @returns {string|null}
 */
export function parseTime(time: any, pattern?: string) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "-")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "");
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key as keyof typeof formatObj];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
}

/**
 * @description 获取localStorage
 * @param {String} key Storage名称
 * @returns {String}
 */
export function localGet(key: string) {
  const value = window.localStorage.getItem(key);
  try {
    return JSON.parse(window.localStorage.getItem(key) as string);
  } catch (error) {
    return value;
  }
}

/**
 * @description 存储localStorage
 * @param {String} key Storage名称
 * @param {*} value Storage值
 * @returns {void}
 */
export function localSet(key: string, value: any) {
  window.localStorage.setItem(key, JSON.stringify(value));
}

/**
 * @description 清除localStorage
 * @param {String} key Storage名称
 * @returns {void}
 */
export function localRemove(key: string) {
  window.localStorage.removeItem(key);
}

/**
 * @description 清除所有localStorage
 * @returns {void}
 */
export function localClear() {
  window.localStorage.clear();
}

/**
 * @description 判断数据类型
 * @param {*} val 需要判断类型的数据
 * @returns {String}
 */
export function isType(val: any) {
  if (val === null) return "null";
  if (typeof val !== "object") return typeof val;
  else return Object.prototype.toString.call(val).slice(8, -1).toLocaleLowerCase();
}

/**
 * @description 生成唯一 uuid
 * @returns {String}
 */
export function generateUUID() {
  let uuid = "";
  for (let i = 0; i < 32; i++) {
    let random = (Math.random() * 16) | 0;
    if (i === 8 || i === 12 || i === 16 || i === 20) uuid += "-";
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
  }
  return uuid;
}

/**
 * 判断两个对象是否相同
 * @param {Object} a 要比较的对象一
 * @param {Object} b 要比较的对象二
 * @returns {Boolean} 相同返回 true，反之 false
 */
export function isObjectValueEqual(a: { [key: string]: any }, b: { [key: string]: any }) {
  if (!a || !b) return false;
  let aProps = Object.getOwnPropertyNames(a);
  let bProps = Object.getOwnPropertyNames(b);
  if (aProps.length != bProps.length) return false;
  for (let i = 0; i < aProps.length; i++) {
    let propName = aProps[i];
    let propA = a[propName];
    let propB = b[propName];
    if (!b.hasOwnProperty(propName)) return false;
    if (propA instanceof Object) {
      if (!isObjectValueEqual(propA, propB)) return false;
    } else if (propA !== propB) {
      return false;
    }
  }
  return true;
}

/**
 * @description 生成随机数
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @returns {Number}
 */
export function randomNum(min: number, max: number): number {
  let num = Math.floor(Math.random() * (min - max) + max);
  return num;
}

/**
 * @description 获取当前时间对应的提示语
 * @returns {String}
 */
export function getTimeState(t: Function) {
  console.log(t);
  let timeNow = new Date();
  let hours = timeNow.getHours();
  if (hours >= 6 && hours <= 10) return `${t("早上好")} ⛅`;
  if (hours >= 10 && hours <= 14) return `${t("中午好")} 🌞`;
  if (hours >= 14 && hours <= 18) return `${t("下午好")} 🌞`;
  if (hours >= 18 && hours <= 24) return `${t("晚上好")} 🌛`;
  if (hours >= 0 && hours <= 6) return `${t("凌晨好")} 🌛`;
}

/**
 * @description 获取浏览器默认语言
 * @returns {String}
 */
export function getBrowserLang() {
  let browserLang = navigator.language ? navigator.language : navigator.browserLanguage;
  let defaultBrowserLang = "";
  if (["cn", "zh", "zh-cn"].includes(browserLang.toLowerCase())) {
    defaultBrowserLang = "zh";
  } else {
    defaultBrowserLang = "en";
  }
  return defaultBrowserLang;
}

/**
 * @description 获取不同路由模式所对应的 url + params
 * @returns {String}
 */
export function getUrlWithParams() {
  const url = {
    hash: location.hash.substring(1),
    history: location.pathname + location.search
  };
  return url[mode];
}

/**
 * @description 使用递归扁平化菜单，方便添加动态路由
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 */
export function getFlatMenuList(menuList: Menu.MenuRoute[]): Menu.MenuRoute[] {
  let newMenuList: Menu.MenuRoute[] = JSON.parse(JSON.stringify(menuList));
  return newMenuList.flatMap(item => [item, ...(item.children ? getFlatMenuList(item.children) : [])]);
}

/**
 * @description 使用递归过滤出需要渲染在左侧菜单的列表 (需剔除 isHide == true 的菜单)
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 * */
export function getShowMenuList(menuList: Menu.MenuRoute[]) {
  let newMenuList: Menu.MenuRoute[] = JSON.parse(JSON.stringify(menuList));
  return newMenuList.filter(item => {
    item.children?.length && (item.children = getShowMenuList(item.children));
    return !item.meta?.isHide;
  });
}

/**
 * @description 使用递归找出所有面包屑存储到 pinia/vuex 中
 * @param {Array} menuList 菜单列表
 * @param {Array} parent 父级菜单
 * @param {Object} result 处理后的结果
 * @returns {Object}
 */
export const getAllBreadcrumbList = (menuList: Menu.MenuRoute[], parent = [], result: { [key: string]: any } = {}) => {
  for (const item of menuList) {
    result[item.path] = [...parent, item];
    if (item.children) getAllBreadcrumbList(item.children, result[item.path], result);
  }
  return result;
};

/**
 * @description 使用递归处理路由菜单 path，生成一维数组 (第一版本地路由鉴权会用到，该函数暂未使用)
 * @param {Array} menuList 所有菜单列表
 * @param {Array} menuPathArr 菜单地址的一维数组 ['**','**']
 * @returns {Array}
 */
export function getMenuListPath(menuList: Menu.MenuRoute[], menuPathArr: string[] = []): string[] {
  for (const item of menuList) {
    if (typeof item === "object" && item.path) menuPathArr.push(item.path);
    if (item.children?.length) getMenuListPath(item.children, menuPathArr);
  }
  return menuPathArr;
}

/**
 * @description 递归查询当前 path 所对应的菜单对象 (该函数暂未使用)
 * @param {Array} menuList 菜单列表
 * @param {String} path 当前访问地址
 * @returns {Object | null}
 */
export function findMenuByPath(menuList: Menu.MenuRoute[], path: string): Menu.MenuRoute | null {
  for (const item of menuList) {
    if (item.path === path) return item;
    if (item.children) {
      const res = findMenuByPath(item.children, path);
      if (res) return res;
    }
  }
  return null;
}

/**
 * @description 使用递归过滤需要缓存的菜单 name (该函数暂未使用)
 * @param {Array} menuList 所有菜单列表
 * @param {Array} keepAliveNameArr 缓存的菜单 name ['**','**']
 * @returns {Array}
 * */
export function getKeepAliveRouterName(menuList: Menu.MenuRoute[], keepAliveNameArr: string[] = []) {
  menuList.forEach(item => {
    item.meta.isKeepAlive && item.name && keepAliveNameArr.push(item.name);
    item.children?.length && getKeepAliveRouterName(item.children, keepAliveNameArr);
  });
  return keepAliveNameArr;
}

/**
 * @description 格式化表格单元格默认值 (el-table-column)
 * @param {Number} row 行
 * @param {Number} col 列
 * @param {*} callValue 当前单元格值
 * @returns {String}
 * */
export function formatTableColumn(row: number, col: number, callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}

/**
 * @description 处理 ProTable 值为数组 || 无数据
 * @param {*} callValue 需要处理的值
 * @returns {String}
 * */
export function formatValue(callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}

/**
 * @description 处理 prop 为多级嵌套的情况，返回的数据 (列如: prop: user.name)
 * @param {Object} row 当前行数据
 * @param {String} prop 当前 prop
 * @returns {*}
 * */
export function handleRowAccordingToProp(row: { [key: string]: any }, prop: string) {
  if (!prop.includes(".")) return row[prop] ?? "--";
  prop.split(".").forEach(item => (row = row[item] ?? "--"));
  return row;
}

/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {String} prop 当前 prop
 * @returns {String}
 * */
export function handleProp(prop: string) {
  const propArr = prop.split(".");
  if (propArr.length == 1) return prop;
  return propArr[propArr.length - 1];
}

/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {String} callValue 当前单元格值
 * @param {Array} enumData 字典列表
 * @param {Array} fieldNames label && value && children 的 key 值
 * @param {String} type 过滤类型（目前只有 tag）
 * @returns {String}
 * */
export function filterEnum(callValue: any, enumData?: any, fieldNames?: FieldNamesProps, type?: "tag") {
  const value = fieldNames?.value ?? "value";
  const label = fieldNames?.label ?? "label";
  const children = fieldNames?.children ?? "children";
  let filterData: { [key: string]: any } = {};
  // 判断 enumData 是否为数组
  if (Array.isArray(enumData)) filterData = findItemNested(enumData, callValue, value, children);
  // 判断是否输出的结果为 tag 类型
  if (type == "tag") {
    return filterData?.tagType ? filterData.tagType : "";
  } else {
    return filterData ? filterData[label] : callValue ? callValue : "--";
  }
}

/**
 * @description 递归查找 callValue 对应的 enum 值
 * */
export function findItemNested(enumData: any, callValue: any, value: string, children: string) {
  return enumData.reduce((accumulator: any, current: any) => {
    if (accumulator) return accumulator;
    if (current[value] === callValue) return current;
    if (current[children]) return findItemNested(current[children], callValue, value, children);
  }, null);
}

interface TreeNode<T> {
  [key: string]: any; // 允许 TreeNode 包含任意属性
}

export function handleTree<T extends TreeNode<T>>(
  data: T[],
  id: keyof T = "id",
  parentId: keyof T = "parentId",
  children: keyof T = "children",
  hasSort: boolean = true,
  sortField: keyof T = "sort"
): T[] {
  let config = {
    id: id,
    parentId: parentId,
    childrenList: children,
    sortField: sortField
  };

  // console.log(config, "configconfigconfigconfig");

  let childrenListMap: Record<string, T[]> = {};
  let nodeIds: Record<string, T> = {};
  let tree: T[] = [];

  // 使用 as string 断言，因为 keyof T 类型不保证一定是 string
  for (let d of data) {
    let parentIdKey = (d[config.parentId] ?? "") as string;
    if (!childrenListMap[parentIdKey]) {
      childrenListMap[parentIdKey] = [];
    }
    nodeIds[d[config.id] as unknown as string] = d;
    childrenListMap[parentIdKey].push(d);
  }

  for (let d of data) {
    let parentIdKey = (d[config.parentId] ?? "") as string;
    if (!nodeIds[parentIdKey]) {
      tree.push(d);
    }
  }
  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o: T) {
    let childrenKey = o[config.id] as unknown as string;

    // 使用类型断言确保 TypeScript 理解这个赋值是合法的
    (o[config.childrenList] as unknown) = childrenListMap[childrenKey] ?? [];

    let childrenArray: T[] = o[config.childrenList] as T[];
    childrenArray.forEach(adaptToChildrenList);
  }

  function sortTree(nodes: T[]): T[] {
    // 对每个节点的子节点数组进行排序
    return nodes
      .sort((a, b) => {
        // 比较逻辑可能需要根据实际数据类型进行调整
        return (a[config.sortField] as number) - (b[config.sortField] as number);
      })
      .map(node => {
        // 如果存在子节点，则递归排序
        if (node[config.childrenList] && node[config.childrenList].length > 0) {
          (node as any)[config.childrenList] = sortTree(node[config.childrenList] as T[]);
        }
        return node;
      });
  }
  return hasSort ? sortTree(tree) : tree;
}

const VITE_PREVIEW_BASE_URL = import.meta.env.VITE_PREVIEW_BASE_URL;

export const formatUrl = (url: string, prefix = VITE_PREVIEW_BASE_URL) => {
  if (isEmpty(url)) return "";
  return url.startsWith("http") ? url : prefix + url;
};

export function findTopParent<T extends Record<string, any>>(
  data: T[],
  targetId: number,
  idFieldName: keyof T = "id",
  parentIdFieldName: keyof T = "parentId"
): T | null {
  function findNodeById(nodes: T[], nodeId: number): T | undefined {
    return nodes.find(node => node[idFieldName] === nodeId);
  }

  function findTopParentNode(nodes: T[], nodeId: number): T | null {
    const node = findNodeById(nodes, nodeId);
    if (!node) {
      return null;
    }
    const parentId = node[parentIdFieldName];
    if (parentId === null || parentId === undefined) {
      return node;
    }
    return findTopParentNode(nodes, parentId);
  }

  return findTopParentNode(data, targetId);
}

export const getMessage = (key: string) => {
  const languageStore = useLanguageStore();
  const message: any = languageStore.languageData.reduce((prev, curr) => {
    return { ...prev, [curr.label]: curr.value };
  }, {});
  // ts-ignore
  return isEmpty(message[key] as any) ? key : message[key];
};

// // 加密
export function encryption(data: any, key: string = PRIVATE_KEY) {
  // Encrypt
  if (isEmpty(data)) return "";

  const res = CryptoJS.AES.encrypt(data, key).toString();
  return res;
}

export const view8d = async (num8d: string) => {
  if (isEmpty(num8d)) return;
  const { data } = await getAppDetail({ code: "8d" });
  return window.open(data.url + "report/detail?reportNo=" + num8d);
};

export const isHttp = (url: string) => {
  return /^http/.test(url);
};

//id转用户名称
export function formatUserName(ids: number | [], staffList: Staff.Item[]): string {
  if (isNumber(ids)) {
    const user = staffList.find(item => item.id == ids);
    if (user?.name) {
      return user.name;
    }
  }
  if (isArray(ids) && ids.length > 0) {
    let str = "";
    ids.forEach(item => {
      str += formatUserName(item, staffList) + ", ";
    });
    return str.substring(0, str.length - 2);
  }
  return "";
}

type DataItem = {
  [key: string]: any;
};

/**
 * 获取对象数组的指定列
 * @param {Array<DataItem>} data - 对象数组
 * @param {Array<keyof DataItem>} columns - 需要获取的列名列表
 * @returns {Array<Partial<DataItem>>} 包含指定列的新对象数组
 */
export function getColumns<T extends DataItem>(data: Array<T>, columns: Array<keyof T>): Array<Partial<T>> {
  return data?.map(item => {
    let newItem: Partial<T> = {};
    columns.forEach(col => {
      if (col in item) {
        newItem[col] = item[col];
      }
    });
    return newItem;
  });
}

//获取去年到当前日期月份
export function getYearStartAndEnd(): { start: string; end: string } {
  const currentDate = new Date();
  const lastYearStart = new Date(currentDate.getFullYear() - 1, 0);
  const formatDate = date => {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    return `${year}-${month.toString().padStart(2, "0")}`;
  };
  return {
    start: formatDate(lastYearStart),
    end: formatDate(currentDate)
  };
}
//根据类型获取初始时间范围
export function getTimeRange(type: string = "month") {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 当前月份（1-12）

  const getWeekNumber = (date: Date): number => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  };

  const padMonthNumber = (month: number): string => {
    return month.toString().padStart(2, "0");
  };

  const padWeekNumber = (week: number): string => {
    return week.toString().padStart(2, "0");
  };

  const padDayNumber = (day: number): string => {
    return day.toString().padStart(2, "0");
  };

  switch (type) {
    case "date":
    case "day": {
      // 近30天
      const startDate = new Date(now);
      startDate.setDate(now.getDate() - 29); // 30天前的日期

      const startYear = startDate.getFullYear();
      const startMonth = padMonthNumber(startDate.getMonth() + 1);
      const startDay = padDayNumber(startDate.getDate());

      const endYear = year;
      const endMonth = padMonthNumber(month);
      const endDay = padDayNumber(now.getDate());

      return {
        start: `${startYear}-${startMonth}-${startDay}`,
        end: `${endYear}-${endMonth}-${endDay}`
      };
    }

    case "month": {
      // 近12个月
      const startDate = new Date(now);
      startDate.setMonth(now.getMonth() - 11); // 12个月前的日期

      const startYear = startDate.getFullYear();
      const startMonth = padMonthNumber(startDate.getMonth() + 1); // 月份从0开始，需要+1

      const endYear = year;
      const endMonth = padMonthNumber(month);

      return {
        start: `${startYear}-${startMonth}`,
        end: `${endYear}-${endMonth}`
      };
    }

    case "quarter": {
      // 近4个季度
      const currentQuarter = Math.ceil(month / 3); // 当前季度（1-4）
      const startQuarter = currentQuarter - 3 >= 1 ? currentQuarter - 3 : 1; // 4个季度前的季度
      const startYear = currentQuarter - 3 >= 1 ? year : year - 1; // 如果跨年处理

      return {
        start: `${startYear}-Q${startQuarter}`,
        end: `${year}-Q${currentQuarter}`
      };
    }

    case "year":
      return {
        start: `${year - 3}`,
        end: `${year}`
      };

    case "week": {
      let currentWeek = getWeekNumber(now);
      let startWeek = currentWeek - 6; // 近7周
      let endWeek = currentWeek;

      // 处理跨年情况
      let startYear = year;
      if (startWeek < 1) {
        startYear = year - 1;
        // 计算上一年的总周数
        const lastDayOfPrevYear = new Date(year - 1, 11, 31);
        const totalWeeksPrevYear = getWeekNumber(lastDayOfPrevYear);
        startWeek += totalWeeksPrevYear;
      }

      return {
        start: `${startYear}-${padWeekNumber(startWeek)}`,
        end: `${year}-${padWeekNumber(endWeek)}`
      };
    }

    default:
      throw new Error(`Invalid type: ${type}. Valid types are: day, month, quarter, year, week.`);
  }
}

//格式化周
export function formatDateToISOWeek(dateString: string) {
  const date = new Date(dateString);
  const year = date.getFullYear();

  // 计算ISO周数
  const target = new Date(date) as any;
  const dayNr = (target.getDay() + 6) % 7; // 获取周几，周一为0，周日为6
  target.setDate(target.getDate() - dayNr + 3); // 调整到周四
  const jan4 = new Date(target.getFullYear(), 0, 4) as any; // 当年的1月4日
  const dayDiff = (target - jan4) / 86400000; // 和1月4日的天数差
  const weekNr = 1 + Math.ceil(dayDiff / 7); // 计算周数

  // 格式化为 YYYY-ww
  return `${year}-${weekNr.toString().padStart(2, "0")}`;
}

//用户搜索回调
export function updateUserSearchExtra(form: any, staff: Staff.Extra, fieldPrefix: string, errorMessageKey?: string) {
  form.value[`${fieldPrefix}No`] = staff.jobNum;
  form.value[`${fieldPrefix}Email`] = staff.email;

  if (!isEmpty(errorMessageKey) && staff.jobNum && isEmpty(staff.email)) {
    ElMessage.error({ message: getMessage(errorMessageKey ?? "") });
  }
  return staff;
}

//转化表格数据
export function transformData(data: any[]) {
  if (!data) return [];

  return data?.map(item => {
    const transformedItem: { [key: string]: any } = { "-": item.rowName };
    item.rowDataList?.forEach(({ columnName, dataValue }) => {
      transformedItem[columnName] = dataValue;
    });
    return transformedItem;
  });
}
export function transformCostData(data: Array<{ statsTime: string; [key: string]: any }>) {
  const columns = Object.keys(data[0]).filter(key => key !== "statsTime");
  const rows = columns.map(col => ({ label: col }));

  data.forEach(({ statsTime, ...values }) => {
    columns.forEach((col, index) => {
      rows[index][statsTime] = values[col];
    });
  });
  return rows;
}

//转化供应商表格数据
export function transformDataForSupplier<T>(
  data: { supplierName: string; rowDataList: { dateLabel: string; [key: string]: any }[] }[] | undefined,
  field: string
): { [key: string]: any }[] {
  if (!data) {
    return [];
  }

  const r = data.map(item => {
    const transformedItem: { [key: string]: any } = { "-": item.supplierName };
    item.rowDataList.forEach(row => {
      if (field in row) {
        transformedItem[row.dateLabel] = row[field];
      }
    });
    return transformedItem;
  });

  return r;
}
//获取转换表格数据和表头
export function generateTableDataAndFields(list: Statistics.rowList[], filterArr: string[], type: "tableData" | "fields") {
  const defaultRes = { tableData: [], fields: [] };

  if (isEmpty(list) || isEmpty(filterArr)) return defaultRes;

  const filteredList = list.filter(item => filterArr.includes(item?.rowName));
  if (isEmpty(filteredList)) return defaultRes;

  // 根据filterArr的顺序以及rowSort进行排序
  filteredList.sort((a, b) => {
    const indexA = filterArr.indexOf(a.rowName);
    const indexB = filterArr.indexOf(b.rowName);
    if (indexA !== indexB) {
      return indexA - indexB; // 按照filterArr的顺序排序
    } else {
      return a.rowSort - b.rowSort; // 如果filterArr顺序相同，按rowSort排序
    }
  });

  return {
    tableData: type === "tableData" ? transformData(filteredList) : [],
    fields: type === "fields" ? ["-", ...filteredList[0].rowDataList.map(item => item.columnName)] : []
  };
}

export function getCurrentMonth() {
  const date = new Date();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 确保月份是两位数
  const year = date.getFullYear();
  return `${year}-${month}`;
}

export function getCurrentDate() {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = ("0" + (currentDate.getMonth() + 1)).slice(-2);
  const day = ("0" + currentDate.getDate()).slice(-2);
  return year + "-" + month + "-" + day;
}

export function reverseJson(text: string | any[]) {
  if (typeof text === "string") {
    return !isEmpty(text) ? JSON.parse(text) : [];
  } else if (Array.isArray(text)) {
    return JSON.stringify(text);
  }
  return "";
}

//判断是否有菜单权限
export function hasMenuPermission(data: any[], permission: string) {
  for (const item of data) {
    if (item.meta && item.meta.perms === permission) {
      return true;
    }
    if (item.children && item.children.length > 0) {
      if (hasMenuPermission(item.children, permission)) {
        return true;
      }
    }
  }
  return false;
}

//格式化数字去掉末尾的零
export function formatNumber(num: number) {
  return parseFloat(num.toString().replace(/\.?0+$/, ""));
}
/**
 * @description 是否可见操作列
 * @param {string[]} powerButtons 权限按钮
 * @param {string[]} buttons 页面上的按钮
 */
export const visibleOperationCol = (powerButtons: string[], buttons: string[]) => {
  // 权限按钮存在于页面按钮 默认管理员拥有所有权限
  if (powerButtons.find(item => item === "*:*:*")) {
    return true;
  } else {
    return powerButtons.some(btn => buttons.includes(btn));
  }
};

/**
 * @description 操作列的列宽
 * @param {string[]} powerButtons 权限按钮
 * @param {string[]} buttons 页面上的按钮
 */
export const getOperationColWidth = (powerButtons: string[], buttons: string[]) => {
  console.log(powerButtons.includes("*:*:*"));

  if (powerButtons.includes("*:*:*")) {
    return buttons.reduce(total => {
      return total + 60;
    }, 0);
  }

  return powerButtons
    .filter(btn => buttons.includes(btn))
    .reduce(total => {
      return total + 60;
    }, 0);
};

/**
 * 转换树形结构
 * @param flatMenus
 */
export function buildMenuTree(flatMenus: { data: MenuItem[] }): MenuItem[] {
  // 初始化映射表，添加children属性并确保它存在
  const menuMap = new Map<number, MenuItem>();
  flatMenus.forEach(menu => {
    menu.id = menu.menuId;
    menuMap.set(menu.menuId, {
      ...menu,
      children: []
    });
  });
  // 构建树形结构
  const menuTree: MenuItem[] = [];
  flatMenus.forEach(menu => {
    const node = menuMap.get(menu.menuId)!;
    if (menu.parentId === 0) {
      menuTree.push(node);
    } else {
      const parent = menuMap.get(menu.parentId);
      if (parent) {
        if (!Array.isArray(parent.children)) parent.children = [];
        parent.children!.push(node);
      }
    }
  });

  // 递归排序函数 - 确保所有层级都按orderNum排序
  const sortMenuTree = (menus: MenuItem[]) => {
    menus.sort((a, b) => a.orderNum - b.orderNum);
    menus.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        sortMenuTree(menu.children);
      }
    });
  };

  // 对根节点排序
  sortMenuTree(menuTree);
  console.log(menuTree, "menuTreemenuTreemenuTreemenuTreemenuTreemenuTreemenuTreemenuTreemenuTreemenuTreemenuTreemenuTree");

  return menuTree;
}
