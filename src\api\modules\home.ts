import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Home } from "@/typings/home";

const baseUrl = "/sys_staff";

export const getToDoList = (params?: ReqPage) => {
  return http.get<Home.Data>(`/sys_todo/list`, params);
};

export const getToDoDetail = (id: number) => {
  return http.get<Home.IToDo>(`/sys_todo/${id}`);
};

export const createToDo = (data: Home.IToDo) => {
  return http.post<Home.IToDo>(`/sys_todo`, data);
};

export const editToDo = (data: Home.IToDo) => {
  return http.put<Home.IToDo>(`/sys_todo`, data);
};
