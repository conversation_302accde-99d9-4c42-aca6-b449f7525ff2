import { Language } from "@/typings/language";
import { App } from "@/typings/app";
import { Staff } from "@/typings/staff";
import { Statistics } from "@/typings/customer-refund/statistics";
import { Dept } from "@/typings/dept";
import { Post } from "@/typings/post";
export type LayoutType = "vertical" | "classic" | "transverse" | "columns";

export type AssemblySizeType = "large" | "default" | "small";

export type LanguageType = "zh" | "en" | null;

/* GlobalState */
export interface GlobalState {
  layout: LayoutType;
  assemblySize: AssemblySizeType;
  language: LanguageType;
  maximize: boolean;
  primary: string;
  isDark: boolean;
  isGrey: boolean;
  isWeak: boolean;
  asideInverted: boolean;
  headerInverted: boolean;
  isCollapse: boolean;
  accordion: boolean;
  breadcrumb: boolean;
  breadcrumbIcon: boolean;
  tabs: boolean;
  tabsIcon: boolean;
  footer: boolean;
  btnAction: string;
  fingerprint: string;
}

/* UserState */
export interface UserState {
  token: string;
  ryToken: string;
  userInfo: any;
}

export interface AppState {
  appInfo: App.Item;
}

/* tabsMenuProps */
export interface TabsMenuProps {
  icon: string;
  title: string;
  path: string;
  name: string;
  close: boolean;
  isKeepAlive: boolean;
}

/* TabsState */
export interface TabsState {
  tabsMenuList: TabsMenuProps[];
}

/* AuthState */
export interface AuthState {
  routeName: string;
  menuId: number;
  workflowId?: number;
  authButtonList: string[];
  authMenuList: any[];
}

export interface LanguageState {
  languageCode: string;
  languageCodeList: Language.Item[];
  // languageData: Language.IDataItem[];
}

/* KeepAliveState */
export interface KeepAliveState {
  keepAliveName: string[];
}

export interface StaffState {
  staff: Staff.Item[];
}

export interface DeptState {
  dept: Dept.Item[];
}
export interface PostState {
  post: Post.Item[];
}
export interface formulaState {
  formula: Statistics.FormulaExplanation[];
}
