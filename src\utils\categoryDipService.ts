import { ref } from "vue";
import { getDipCategoryList } from "@/api/modules/ipqc-dip/ipqc_dip_category";
import { IpqcDipCategory } from "@/typings/ipqc-dip/ipqc_dip_category";

export const categoryList = ref<IpqcDipCategory.Item[]>([]);
export const getCategoryList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getDipCategoryList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      categoryList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
