<script setup lang="ts" name="TodoList">
import { ref, onMounted, reactive, watchEffect, computed } from "vue";
import { getStaffList } from "@/api/modules/staff";
import { getToDoList } from "@/api/modules/home";
import { useDateFormat } from "@vueuse/core";
import { ReqPage } from "@/api/interface";
import ToDoModal from "./ToDoModal.vue";
import { Staff } from "@/typings/staff";
import { useRouter } from "vue-router";
import { Home } from "@/typings/home";
import { App } from "@/typings/app";

interface IProps {
  admin: App.Item;
}

const props = defineProps<IProps>();

const router = useRouter();

const toDoData = ref<Home.Data>({} as Home.Data);

const toDoModalRef = ref<InstanceType<typeof ToDoModal>>();

const todoId = ref<number>();

const staffList = ref<Staff.Item[]>([]);

const queryParams = reactive<ReqPage & { status: string }>({
  pageNum: 1,
  pageSize: 10,
  status: "0"
});

const listTotal = ref(0);
const admin = computed(() => props.admin);
const getData = async () => {
  const { data } = await getToDoList(queryParams);
  listTotal.value = data.list.total;
  toDoData.value = data;
};

const onChangeTodo = (status: string) => {
  queryParams.status = status;
};

const handleData = ({ path, appCode, url, title, menuId }: Home.IToDo) => {
  const isAdmin = appCode === admin.value.code;
  if (isAdmin) {
    return router.push({ path });
  }
  const toUrl = `${url}?appCode=${appCode}&title=${title}&menuId=${menuId}&path=${path}`;
  window.open(toUrl, "_blank");
};

const handleSizeChange = async (size: number) => {
  queryParams.pageSize = size;
};

const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
};

const _getStaffList = async () => {
  const {
    data: { list }
  } = await getStaffList();
  staffList.value = list;
};

watchEffect(async () => {
  queryParams;
  await getData();
});

onMounted(async () => {
  await _getStaffList();

  await getData();
});
</script>

<template>
  <div class="flex flex-col gap-4 card">
    <div class="text-[20px] font-bold flex-shrink-0">{{ $t("待办事项") }}</div>
    <div class="flex flex-shrink-0 gap-2">
      <div
        class="py-2 px-4 flex justify-between bg-[#009DFF] text-white gap-10 rounded cursor-pointer"
        @click="onChangeTodo('0')"
      >
        <span>{{ $t("待处理") }}</span>
        <span>{{ toDoData.pending }}{{ $t("条") }}</span>
      </div>
      <div
        class="py-2 px-4 flex justify-between bg-[#67c23A] text-white gap-10 rounded cursor-pointer"
        @click="onChangeTodo('1')"
      >
        <span>{{ $t("已处理") }}</span>
        <span>{{ toDoData.processed }}{{ $t("条") }}</span>
      </div>
      <div
        class="py-2 px-4 flex justify-between bg-[#F56C6C] text-white gap-10 rounded cursor-pointer"
        @click="onChangeTodo('2')"
      >
        <span>{{ $t("已拒绝") }}</span>
        <span>{{ toDoData.rejected }}{{ $t("条") }}</span>
      </div>
    </div>
    <div class="flex flex-col gap-2">
      <el-empty v-if="!toDoData.list?.list || toDoData.list?.list.length < 1" :description="$t('暂无待办事项')" />

      <template v-else>
        <div
          v-for="item of toDoData.list?.list"
          class="flex items-centet cursor-pointer justify-between w-full transition-all p-2 border-l-2 border-[#009DFF] hover:bg-[#9093991A]"
          :key="item.id"
        >
          <div class="flex flex-col gap-1">
            <div class="text-base">{{ $t(item.title) }}</div>
            <div class="text-[#7E7979] text-sm">
              {{ $t("申请时间") }}: {{ useDateFormat(item.createdTime, "YYYY-MM-DD HH:mm:ss").value }}
            </div>
          </div>
          <div v-if="queryParams.status === '0'">
            <el-button type="primary" @click.stop="handleData(item)">{{ $t("去处理") }}</el-button>
          </div>
          <div v-else>
            <el-button type="primary" @click.stop="handleData(item)">{{ $t("查看") }}</el-button>
          </div>
        </div>
      </template>
    </div>
    <div>
      <el-pagination
        :background="true"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 25, 50, 100]"
        :total="listTotal"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <ToDoModal ref="toDoModalRef" :todo-id="todoId" :staff-list="staffList" :admin="admin" @submit-success="getData" />
</template>
