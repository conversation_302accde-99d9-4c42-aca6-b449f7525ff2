<template>
  <el-dialog v-model="visible" width="700px" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form ref="formRef" label-width="150px" label-suffix=" :" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <el-select v-model="form.supplierName">
              <el-option v-for="(item, index) in supplierList" :key="index" :label="item.supplier" :value="item.supplier" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('升级规则')" prop="daysThreshold">
            <el-select v-model="form.daysThreshold">
              <el-option v-for="{ label, value } of exception_rules" :key="value" :label="label" :value="value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('节点')" prop="applicableNodes">
            <el-select v-model="form.applicableNodes" multiple>
              <el-option v-for="{ label, value } of report_node_type_8d" :key="value" :label="label" :value="value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('升级对象')">
            <el-select v-model="form.userIds" multiple>
              <el-option v-for="{ userId, nickName } of responsibleList" :key="userId" :label="nickName" :value="userId" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="RoleModal">
import { ExceptionManageForm } from "@/typings/exception-manage";
import { getPublicUser, getStaffList } from "@/api/modules/staff";
import { ElMessage, FormInstance, ElTreeSelect } from "element-plus";
import { ref, reactive, toRefs } from "vue";
import { isEmpty } from "@/utils/is";
import { useI18n } from "vue-i18n";
import { useDict } from "@/hooks/useDict";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
const { exception_rules, report_node_type_8d } = useDict("exception_rules", "report_node_type_8d");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<ExceptionManageForm>;
  // common_status: Dict.IDataItem[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  fileUrls?: any[];
  responsibleList: any[];
}

const { t } = useI18n();

const rules = {
  daysThreshold: [{ required: true, message: t("请选择"), trigger: "blur" }],
  supplierName: [{ required: true, message: t("请填写"), trigger: "blur" }],
  applicableNodes: [{ required: true, message: t("请选择"), trigger: "blur" }],
  userNames: [{ required: true, message: t("请选择"), trigger: "blur" }]
};

// const treeSelectRef = ref<InstanceType<typeof ElTreeSelect> | null>();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  // common_status: [],
  form: {},
  fileUrls: [],
  responsibleList: []
});

// const menuList = ref<Menu.Item[]>([]);

const { form, title, isView, fileUrls, responsibleList } = toRefs(state);
const supplierList = ref<Supplier.Item[]>([]);

const setVisible = (val: boolean) => {
  visible.value = val;
};

const getAllStaff = async () => {
  const { data } = await getPublicUser({ pageNum: 1, pageSize: 999 });
  responsibleList.value = data.list;
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  if (params.form.daysThreshold) params.form.daysThreshold = String(params.form.daysThreshold);
  Object.assign(state, params);
  await getAllStaff();
  await getSupplierData();
  setVisible(true);
};
// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const _form = JSON.parse(JSON.stringify(form.value));
      const applicableNodes = _form.applicableNodes;
      const userIds = _form.userIds;
      delete _form.userNames;
      await state.api!({
        ..._form,
        applicableNodes: applicableNodes.join(","),
        userIds: userIds.join(",")
      });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style>
.tree-border {
  width: 100%;
  margin-top: 5px;
  background: #ffffff none;
  border: 1px solid #e5e6e7;
  border-radius: 4px;
}
</style>
