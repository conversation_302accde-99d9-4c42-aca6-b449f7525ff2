<template>
  <div class="table-box">
    <ProTable ref="proTable" :pagination="false" :columns="columns" :request-api="getTableList" :init-param="initParam">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'system:language:import'" type="primary" @click="handleImport">{{ $t("导入") }}</el-button>
        <el-button v-auth="'system:language:export'" type="primary" @click="handleExport">{{ $t("导出") }}</el-button>
      </template>
    </ProTable>
    <ImportExcel ref="importExcelRef" />
  </div>
</template>

<script setup lang="tsx" name="language">
import { getLanguageList, messagesExport, messagesUploadExcelData } from "@/api/modules/language";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import ImportExcel from "./components/ImportExcel.vue";
import EditModal from "./components/EditModal.vue";
import { useDateFormat } from "@vueuse/core";

import { ref, reactive, toRefs } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { Post } from "@/typings/post";
import { Language } from "@/typings/language";
import { useI18n } from "vue-i18n";
import { languages } from "@/utils/dicts";
import { ipqaDimensionsImport, ipqaMeasureExportTmpl } from "@/api/modules/productionData";
import { downloadFileByName, downloadTemplateFie } from "@/utils/download";

const importExcelRef = ref();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const editModalRef = ref<InstanceType<typeof EditModal> | null>(null);
const { t } = useI18n();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)zh-CN/en-US
const initParam = reactive({ code: "zh-CN" });

// 表格配置项
const columns = reactive<ColumnProps<Post.Item>[]>([
  { type: "index", label: "序号", width: 80 },
  {
    prop: "code",
    label: "类型",
    enum: languages,
    fieldNames: { label: "label", value: "value" },
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.code} placeholder={t("请选择")}>
            {languages.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },

  {
    prop: "label",
    label: "语言名称",
    render: row => {
      return row.label;
    }
  },
  {
    prop: "value",
    label: "语言编码",
    render: row => {
      return row.value;
    }
  },
  {
    prop: "createdTime",
    label: "创建时间",
    render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  }
  // ...(visibleOperationCol(auth.authButtonList, pageButtons)
  //   ? [
  // {
  //   prop: "operation",
  //   label: "操作",
  //   width: 200,
  //   fixed: "right"
  // }
  //     ]
  //   : [])
]);

// const dataCallback = (data: Language.Item) => {
//   return {
//     list: data
//   };
// };

const getTableList = (params: any) => {
  params.type = params.code;
  return getLanguageList({ ...params });
};

const handleImport = () => {
  const params = {
    title: "导入",
    importApi: messagesUploadExcelData,
    // tempApi: ipqaMeasureExportTmpl,
    // tempFun: downloadTemplateFie,
    getTableList: proTable.value?.getTableList
  };
  importExcelRef.value.acceptParams(params);
};

const handleExport = async () => {
  await downloadFileByName(messagesExport, { type: initParam.code });
};
// const openDictModal = (title: string, row: Partial<Post.Item> = {}) => {
//   if (title !== "新增") {
//     check();
//   }
//   const form = title === "新增" ? {} : { ...currentRow.value };
//   const params = {
//     title,
//     isView: title === "查看",
//     form,
//     common_status: common_status.value,
//     api: title === "新增" ? createPost : title === "编辑" ? editPost : void 0,
//     getTableList: proTable.value?.getTableList
//   };
//   if (isEmpty(form.id)) Object.assign(params.form, { status: "0" });
//   editModalRef.value?.acceptParams(params);
// };

// ;
// };
</script>
