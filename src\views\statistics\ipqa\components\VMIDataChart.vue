<template>
  <div class="card table-main">
    <div class="tab-wrap">
      <el-radio-group v-model="currentPage" size="large" @change="tabChange">
        <el-radio label="Yield" :value="1" />
        <el-radio label="TrendDefect Pareto" :value="2" />
      </el-radio-group>
    </div>
    <div v-if="currentPage === 1" class="yield-trend-chart chart-item" ref="yieldTrend"></div>
    <div v-if="currentPage === 2" class="defect-pareto-chart chart-item" ref="defectPareto"></div>
  </div>
</template>

<script setup lang="ts" name="ipqaChartCard">
import { onMounted, ref } from "vue";
import { ipqaVmiDataAnalysis, ipqaVmiDataParetoAnalysis } from "@/api/modules/productionDataAnalysis";
import { ipqaYield, defectParetoChart } from "@/views/statistics/charts";
const currentPage = ref(1);

const queryParams = ref();
const yieldTrend = ref();
const defectPareto = ref();

const tabChange = async () => {
  await setChart();
};
const getData = async (query: any) => {
  queryParams.value = query;
  await setChart();
};

const setChart = async () => {
  if (currentPage.value === 1) {
    const { data } = await ipqaVmiDataAnalysis(queryParams.value);
    ipqaYield({ ref: yieldTrend.value, list: data });
  }
  if (currentPage.value === 2) {
    const { data } = await ipqaVmiDataParetoAnalysis(queryParams.value);
    defectParetoChart({ ref: defectPareto.value, list: data });
  }
};

defineExpose({ getData });
</script>
<style scoped lang="scss">
.tab-wrap {
  text-align: center;
}
.table-main {
  margin-bottom: 10px;
}
.chart-item {
  height: 400px;
  width: 100%;
  margin-top: 30px;
}
</style>
