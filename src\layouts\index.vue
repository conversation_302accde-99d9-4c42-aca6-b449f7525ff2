<!-- 💥 这里是一次性加载 LayoutComponents -->
<template>
  <component :is="LayoutComponents[layout]" />
  <ThemeDrawer />
  <FullScreenTable />
</template>

<script setup lang="ts" name="layout">
import { computed, onMounted, type Component } from "vue";
import { LayoutType } from "@/stores/interface";
import { useGlobalStore } from "@/stores/modules/global";
import ThemeDrawer from "./components/ThemeDrawer/index.vue";
import FullScreenTable from "./components/FullScreenTable/index.vue";
import LayoutVertical from "./LayoutVertical/index.vue";
import LayoutClassic from "./LayoutClassic/index.vue";
import LayoutTransverse from "./LayoutTransverse/index.vue";
import LayoutColumns from "./LayoutColumns/index.vue";
import useAppStore from "@/stores/modules/app";
import { APP_CODE } from "@/config";

// import usePostStore from "@/stores/modules/post";
// import useDeptStore from "@/stores/modules/dept";
import useFormulaStore from "@/stores/modules/formula";
// const postStore = usePostStore();
// const deptStore = useDeptStore();
// postStore.getPost();
// deptStore.getDept();

const formulaStore = useFormulaStore();
formulaStore.getFormula();

const LayoutComponents: Record<LayoutType, Component> = {
  vertical: LayoutVertical,
  classic: LayoutClassic,
  transverse: LayoutTransverse,
  columns: LayoutColumns
};

const globalStore = useGlobalStore();
const appStore = useAppStore();
const layout = computed(() => globalStore.layout);
onMounted(async () => {
  await appStore.getAppInfo(APP_CODE);
});
</script>

<style scoped lang="scss">
.layout {
  min-width: 600px;
}
</style>
<style>
.el-dialog .el-dialog__header {
  padding: 0 0 16px;
}
.el-dialog__body {
  padding-top: 20px;
}
</style>
