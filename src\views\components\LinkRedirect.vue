<script setup lang="ts">
import { openOAUrl } from "@/api/modules/common";
import { isEmpty } from "@/utils/is";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps<{
  appCode: string;
  oa: string;
  name?: string;
}>();

const redirect = async (appCode: string, requestid: string) => {
  try {
    const { success, data } = await openOAUrl({ appCode, requestid });
    if (success && !isEmpty(data)) {
      window.open(data as any);
    } else {
      ElMessage.error(t("链接不存在"));
    }
  } catch (e) {
    console.log(e);
  }
};
</script>

<template>
  <el-link v-if="oa" type="primary" @click="redirect(appCode, oa)"> {{ !isEmpty(name) ? name : oa }} </el-link>
  <span v-else-if="!oa && !name"> {{ name }} </span>
  <span v-else> - </span>
</template>
