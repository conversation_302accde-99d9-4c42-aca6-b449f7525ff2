import { isEmpty } from "@/utils/is";

export const formatParams = (p1: any, p2: any, date?: any[]) => {
  delete p1.pageNum;
  delete p1.pageSize;
  const condition = {
    ...p1
  };

  if (!isEmpty(date) && date!.length > 0) {
    condition.startDate = date![0];
    condition.endDate = date![1];
  } else {
    condition.startDate = "";
    condition.endDate = "";
  }

  if (isEmpty(date)) {
    delete condition.startDate;
    delete condition.endDate;
  }

  const result = {
    pageNum: p2.pageNum,
    pageSize: p2.pageSize,
    condition
  };

  delete result.condition.condition;

  return result;
};
