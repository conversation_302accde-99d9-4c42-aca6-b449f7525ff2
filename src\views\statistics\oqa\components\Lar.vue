<template>
  <div class="yield-trend-chart chart-item" ref="larRef"></div>
</template>

<script setup lang="ts" name="ipqaChartCard">
import { onMounted, ref } from "vue";
import { oqaVmiDataAnalysis } from "@/api/modules/productionDataAnalysis";
import { larChart } from "@/views/statistics/charts";
const props = withDefaults(defineProps<{ queryParams: any }>(), {});

const larRef = ref();

const setChart = async () => {
  const { data } = await oqaVmiDataAnalysis(props.queryParams);
  larChart({ ref: larRef.value, list: data });
};

onMounted(() => {
  setChart();
});

// defineExpose({ getData });
</script>
<style scoped lang="scss">
.tab-wrap {
  text-align: center;
}
.table-main {
  margin-bottom: 10px;
}
.chart-item {
  height: 400px;
  width: 100%;
  margin-top: 30px;
}
</style>
