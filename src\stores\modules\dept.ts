import { defineStore } from "pinia";
import { DeptState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { Dept } from "@/typings/dept";
import { getDeptAll, getDeptList } from "@/api/modules/dept";

const useDeptStore = defineStore({
  id: "dept",
  state: (): DeptState => ({
    dept: [] as Dept.Item[]
  }),
  getters: {},
  actions: {
    setDept(dept: Dept.Item[]) {
      this.dept = dept;
    },
    async getDept() {
      const { data } = await getDeptList();
      const dept = data.map(dept => ({
        name: dept.deptName,
        id: dept.deptId
      }));
      this.setDept(dept as any);
    }
  },
  persist: piniaPersistConfig("dept")
});

export default useDeptStore;
