import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { getUserInfo, getRyToken } from "@/api/modules/auth";
import { isEmpty } from "@/utils/is";
import useLanguageStore from "./language";

export const useUserStore = defineStore({
  id: "geeker-user",
  state: (): UserState => ({
    token: "",
    ryToken: "",
    userInfo: { name: "<PERSON><PERSON>" }
  }),
  getters: {},
  actions: {
    // Set Token
    async setToken(token: string) {
      this.token = token;
      this.ryToken = "";
      if (isEmpty(token)) return;
      // const { success: rySuccess, data: ryData } = await getRyToken(token);
      // if (rySuccess) {
      //   this.ryToken = ryData.token;
      // }
      const data = await getUserInfo();
      if (data.code === 200) {
        // await useLanguageStore().setLanguageCode(data.languageCode);
        this.setUserInfo(data.user);
      }
    },
    // Set setUserInfo
    setUserInfo(userInfo: UserState["userInfo"]) {
      // console.log(userInfo);
      this.userInfo = userInfo;
    }
  },
  persist: piniaPersistConfig("geeker-user")
});

export default useUserStore;
