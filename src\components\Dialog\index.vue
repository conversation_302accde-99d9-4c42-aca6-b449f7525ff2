<template>
  <el-dialog
    v-bind="$attrs"
    :fullscreen="fullscreen"
    :show-close="false"
    :modal="!isMini"
    :class="['ds-dialog', fullscreen ? 'fullscreen' : 'not-fullscreen', isMini && 'minisize']"
    :close-on-click-modal="!isMini"
    align-center
    ref="dialogRef"
  >
    <template #header="headerProps">
      <span>
        {{ $attrs.title }}
      </span>
      <div class="header-right">
        <el-icon size="22" class="el-icon--left" @click="setFullscreen(!fullscreen)">
          <FullScreen v-if="!fullscreen" />
          <Minus v-if="fullscreen" />
        </el-icon>

        <el-icon size="22" class="el-icon--left" @click="headerProps.close">
          <Close />
        </el-icon>
      </div>
      <slot name="header" v-bind="headerProps"></slot>
    </template>
    <slot name="default"></slot>
    <template #footer="footerProps">
      <slot name="footer" v-bind="footerProps"></slot>
    </template>
  </el-dialog>
</template>
<script name="DsDialog" lang="ts" setup>
import { FullScreen, Minus } from "@element-plus/icons-vue";
import { ElDialog } from "element-plus";
import { ref } from "vue";

interface IProps {
  defaultFullscreen: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  defaultFullscreen: false
});

const dialogRef = ref<InstanceType<typeof ElDialog> | null>(null);

const fullscreen = ref(props.defaultFullscreen);

const isMini = ref(false);

const setFullscreen = (status: boolean) => {
  fullscreen.value = status;
};
</script>
<style lang="scss">
.ds-dialog {
  display: flex;
  flex-direction: column;
  margin: 0;
  &.minisize {
    position: absolute;
    bottom: 0;
    left: 2%;
    width: 160px;
    height: 48px;
    overflow: hidden;
    .el-dialog__header {
      // padding: 12px 0;
      justify-content: flex-start;
    }
  }
  .el-dialog__header {
    position: relative;
    display: flex;
    align-items: center;
    height: 48px;
    padding: 12px;
    margin: 0;
    font-size: 18px;
    font-weight: normal;
    .header-right {
      position: absolute;
      right: 0;
      display: flex;
      justify-content: center;
      cursor: pointer;
    }
  }
  .el-dialog__body {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  --s: 1000;
  .dialog-footer {
    $scale: calc(var(--s) / 1262);

    text-align: center;
    .el-button {
      // width: percentage(calc(161/1262));
      width: calc(160px * $scale);
      height: calc(48px * $scale);
    }
    .el-button + .el-button {
      margin-left: 24px;
    }
  }
}
</style>
