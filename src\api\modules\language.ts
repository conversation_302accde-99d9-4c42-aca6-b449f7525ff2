import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Language } from "@/typings/language";
import { API_PREFIX } from "@/api/config/servicePort";

export const getLanguageList = (params?: any) => {
  return http.get<ResPage<Language.Item>>(`${API_PREFIX}/messages/list`, params);
};
export const getAllLanguageList = (params?: any) => {
  return http.get<ResPage<Language.Item>>(`${API_PREFIX}/messages/listAll`, params);
};
export const messagesUploadExcelData = (params?: any, type: string) => {
  return http.post(`${API_PREFIX}/messages/uploadExcelData?type=${type}`, params);
};
export const messagesExport = (params?: any) => {
  return http.get(`${API_PREFIX}/messages/export`, params);
};
// export const getLanguageList = (params?: ReqPage) => {
//   return http.get<ResPage<Language.Item>>(`${baseUrl}/code/list`, params);
// };
//
// export const getAllLanguageList = (params?: ReqPage) => {
//   return http.get<Language.Item[]>(`${baseUrl}/code/listAll`, params);
// };
//
// export const getLanguageDetail = (fileId: number) => {
//   return http.get<Language.Item>(`${baseUrl}/${fileId}`);
// };
//
// export const createLanguage = (data: Language.Item) => {
//   return http.post(`${baseUrl}`, data);
// };
//
// export const editLanguage = (data: Language.Item) => {
//   return http.put(`${baseUrl}`, data);
// };
//
// export const deleteLanguage = (data: { ids: number[] }) => {
//   return http.delete(`${baseUrl}/code`, data);
// };
//
// export const getImportTemplate = () => {
//   return http.get(`${baseUrl}/import_template`);
// };
