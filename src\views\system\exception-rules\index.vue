<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" v-auth="'exception-rules:add'" @click="handleModalOperation('新增')">{{
          $t("新增")
        }}</el-button>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button
          v-auth="'exception-rules:remove'"
          type="danger"
          plain
          :disabled="!isSelected"
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("批量删除") }}
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button v-auth="'exception-rules:edit'" type="primary" link @click="handleModalOperation('修改', row)">{{
          t("修改")
        }}</el-button>
      </template>
    </ProTable>
    <handleModal ref="handleModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { isArray, isEmpty } from "@/utils/is";
import { getOperationColWidth, visibleOperationCol } from "@/utils";
import { useAuthStore } from "@/stores/modules/auth";
import { getExceptionRules, exceptionRulesAdd, exceptionRulesEdit, exceptionRulesRemove } from "@/api/modules/exceptionRules";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { DimensionsItem, DimensionsQuery } from "@/typings/dimensions";
import handleModal from "./components/handleModal.vue";
import { useDict } from "@/hooks/useDict";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
import { formatParams } from "@/utils/util";
import { useI18n } from "vue-i18n";
const handleModalRef = ref<InstanceType<typeof handleModal> | null>();
const proTable = ref<ProTableInstance>();
const { exception_rules, report_node_type_8d } = useDict("exception_rules", "report_node_type_8d");

const { check, currentRow } = useCheckSelectId();

const initParam = reactive({ pageNum: 1, pageSize: 10, condition: {} });
const supplierList = ref<Supplier.Item[]>([]);
const { t } = useI18n();
const pageButtons = ["exception:edit", "exception:remove"];
const auth = useAuthStore();
const columns = reactive<ColumnProps<DimensionsItem>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "supplierName",
    label: "供应商",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    },
    width: 200
  },
  {
    prop: "daysThreshold",
    label: "升级规则",
    render: ({ row }) => {
      const obj = exception_rules.value.find(item => item.value == row.daysThreshold);
      return obj ? obj.label : "";
    }
  },
  {
    prop: "applicableNodes",
    label: "节点",
    render: ({ row }) => {
      let list = report_node_type_8d.value
        .map(item => {
          if (row.applicableNodes.includes(item.value)) {
            return item.label;
          }
        })
        .filter(item => item);
      return list.join(",");
    }
  },
  { prop: "userNames", label: "升级对象" },
  ...(visibleOperationCol(auth.authButtonList, pageButtons)
    ? [
        {
          prop: "operation",
          label: "操作",
          width: getOperationColWidth(auth.authButtonList, pageButtons),
          fixed: "right"
        }
      ]
    : [])
]);

const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: DimensionsQuery) => {
  const result = formatParams(initParam, params);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return getExceptionRules(result);
};

// 批量删除用户信息
const batchDelete = async (id: number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(exceptionRulesRemove, { ids }, t("删除所选信息"));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

/**
 * 新增
 */
const handleModalOperation = (title: string, row?: DimensionsItem) => {
  const form = isEmpty(row?.id) ? { active: 1 } : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    // common_status: common_status.value,
    api: isEmpty(row?.id) ? exceptionRulesAdd : exceptionRulesEdit,
    getTableList: proTable.value?.getTableList,
    fileUrls: !isEmpty(row?.id) ? [{ name: row?.attachmentName, url: row?.attachmentUrl }] : []
  };
  if (!isEmpty(row?.id)) {
    let userIds = row?.userIds.split(",");
    userIds = userIds.map(item => Number(item));
    Object.assign(params.form, {
      applicableNodes: row?.applicableNodes.split(","),
      userIds: userIds
    });
  }

  handleModalRef.value?.acceptParams(params);
};
const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};
onMounted(async () => {
  await getSupplierData();
});
</script>
