import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";
import { ExceptionReportsNodeItem } from "@/typings/exceptionReports";

/**
 * 报告节点列表
 * @param params
 */
export const exceptionReportsNodeList = (params: any) => {
  return http.get<ExceptionReportsNodeItem>(`${API_PREFIX}/exceptionReports/nodeList/` + params.id);
};

/**
 * 节点保存
 * @param params
 */
export const exceptionReportsNodeSave = (params: any) => {
  return http.post<ExceptionReportsNodeItem>(`${API_PREFIX}/exceptionReports/node/save`, params);
};

/**
 * 节点提交
 * @param params
 */
export const exceptionReportsNodeSubmit = (params: any) => {
  return http.post<ExceptionReportsNodeItem>(`${API_PREFIX}/exceptionReports/node/submit`, params);
};
