import { ref } from "vue";
import { getCustomerSatBuCfgList } from "@/api/modules/customer-satisfaction/customer_sat_bu_cfg";
import { CustomerSatBuCfg } from "@/typings/customer-satisfaction/customer_sat_bu_cfg";

export const buList = ref<CustomerSatBuCfg.Item[]>([]);
export const getBuList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getCustomerSatBuCfgList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      buList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
