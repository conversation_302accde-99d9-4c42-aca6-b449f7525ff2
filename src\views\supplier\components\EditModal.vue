<template>
  <el-dialog v-model="visible" width="50%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="20">
          <el-col :span="12">
            <el-form-item :label="$t('供应商')" prop="supplier">
              <el-input v-model="form.supplier" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12">-->
          <!--            <el-form-item :label="$t('岗位编码')" prop="postCode">-->
          <!--              <el-input v-model="form.postCode" :placeholder="$t('请填写字典别名')" clearable />-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <!--          <el-col :span="12">-->
          <!--            <el-form-item :label="$t('说明')">-->
          <!--              <el-input-->
          <!--                type="textarea"-->
          <!--                :autosize="{ minRows: 2, maxRows: 4 }"-->
          <!--                v-model="form.remark"-->
          <!--                :placeholder="$t('请填写说明')"-->
          <!--                clearable-->
          <!--              />-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <!--          <el-col>-->
          <!--            <el-form-item :label="$t('状态')" prop="status">-->
          <!--              <el-radio-group v-model="form.status">-->
          <!--                <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{-->
          <!--                  $t(label)-->
          <!--                }}</el-radio>-->
          <!--              </el-radio-group>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { useDict } from "@/hooks/useDict";
import { Supplier } from "@/typings/supplier";
const { common_status } = useDict("common_status");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Supplier.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  supplier: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value, postSort: 1 });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
