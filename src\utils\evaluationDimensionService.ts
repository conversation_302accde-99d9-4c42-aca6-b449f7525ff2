import { ref } from "vue";
import { getQualityRewardPunishmentRuleList } from "@/api/modules/quality-reward-punishment/quality_reward_punishment_rule";
import { QualityRewardPunishmentRule } from "@/typings/quality-reward-punishment/quality_reward_punishment_rule";

export const rules = ref<QualityRewardPunishmentRule.Item[]>([]);
export const getQualityRewardPunishmentRuleListService = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getQualityRewardPunishmentRuleList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      rules.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
