<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" :data-callback="dataCallback">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'system:role:add'" type="primary" @click="openRoleModal('新增')">{{ $t("新增") }}</el-button>
        <el-button v-auth="'system:role:edit'" type="primary" @click="openRoleModal('编辑')">{{ $t("编辑") }}</el-button>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button
          v-auth="'system:role:remove'"
          type="danger"
          plain
          :disabled="!isSelected"
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <RoleModal ref="roleModalRef" />
  </div>
</template>

<script setup lang="tsx" name="RoleTable">
import { createRole, deleteRole, editRole, getRoleList } from "@/api/modules/role";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import RoleModal from "./components/RoleModal.vue";
import { getMenuList } from "@/api/modules/menu";
import { ref, reactive, onMounted } from "vue";
import { isArray, isEmpty } from "@/utils/is";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
import { Role } from "@/typings/role";
import { Menu } from "@/typings/menu";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { common_status } from "@/utils/dicts";
import { getOperationColWidth, visibleOperationCol } from "@/utils";
// const { common_status } = useDict("common_status");
const { t } = useI18n();
const roleModalRef = ref<InstanceType<typeof RoleModal> | null>();

const proTable = ref<ProTableInstance>();

const { check, currentRow } = useCheckSelectId();

const initParam = reactive({ type: 1 });
const menuList = ref<Menu.Item[]>([]);
const pageButtons = ["rolw:edit", "system:menu:add", "system:menu:remove"];

const columns = reactive<ColumnProps<Role.Item>[]>([
  { type: "selection", fixed: "left", width: 70 },
  { type: "index", label: "序号", width: 70 },
  { prop: "roleName", label: "角色名称", width: 120, search: { el: "input" } },
  {
    prop: "permissions",
    label: "权限",
    render: ({ row }) => {
      return (
        <>
          {row.permissions &&
            row.permissions!.map(p => {
              const permission = menuList.value!.find(item => item.id === p && !item.parentId);
              if (permission) {
                return (
                  <el-tag type="success" style={{ marginRight: "10px" }}>
                    {permission.title}
                  </el-tag>
                );
              }
            })}
        </>
      );
    }
  },
  { prop: "remark", label: "备注" },
  {
    prop: "status",
    label: "状态",
    width: 120,
    tag: true,
    enum: common_status,
    search: { el: "select" },
    render: ({ row }) => {
      const obj = common_status.find(v => row.status == v.value);
      return <span>{obj.label}</span>;
    }
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 180
  }
]);

const dataCallback = (data: any) => {
  return {
    list: data.list.map(item => {
      item.id = item.roleId;
      return item;
    }),
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  return getRoleList(params);
};

const getAllMenuList = async () => {
  const { data } = await getMenuList();
  menuList.value = data;
};

// 批量删除用户信息
const batchDelete = async (id: number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(deleteRole, ids.join(","), "删除所选信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openRoleModal = (title: string) => {
  if (title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    form,
    common_status: common_status,
    api: title === "新增" ? createRole : title === "编辑" ? editRole : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) {
    Object.assign(params.form, { status: "0", permissions: [] });
  }
  roleModalRef.value?.acceptParams(params);
};

onMounted(async () => {
  await getAllMenuList();
});
</script>
