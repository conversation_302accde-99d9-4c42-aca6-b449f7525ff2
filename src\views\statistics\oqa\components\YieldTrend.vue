<template>
  <div class="yield-trend-chart chart-item" ref="yieldTrend"></div>
</template>

<script setup lang="ts" name="ipqaChartCard">
import { onMounted, ref } from "vue";
import { ipqaVmiDataAnalysis } from "@/api/modules/productionDataAnalysis";
import { ipqaYield } from "@/views/statistics/charts";
const props = withDefaults(defineProps<{ queryParams: any }>(), {});

const yieldTrend = ref();

const setChart = async () => {
  const { data } = await ipqaVmiDataAnalysis(props.queryParams);
  ipqaYield({ ref: yieldTrend.value, list: data });
};

onMounted(() => {
  setChart();
});

// defineExpose({ getData });
</script>
<style scoped lang="scss">
.tab-wrap {
  text-align: center;
}
.table-main {
  margin-bottom: 10px;
}
.chart-item {
  height: 400px;
  width: 100%;
  margin-top: 30px;
}
</style>
