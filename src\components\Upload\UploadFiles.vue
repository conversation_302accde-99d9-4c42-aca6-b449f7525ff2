<template>
  <div class="upload-file">
    <el-upload
      v-if="showDelete"
      action="#"
      ref="fileUpload"
      class="upload-file-uploader"
      :multiple="multiple"
      :limit="limit"
      :on-error="uploadError"
      :on-exceed="handleExceed"
      :http-request="handleHttpUpload"
      :show-file-list="false"
    >
      <el-button type="primary">{{ btnName }}</el-button>
    </el-upload>
    <div class="el-upload__tip" v-if="showTip && showDelete">
      <slot name="tip"></slot>
    </div>
    <transition-group
      v-if="showFileList"
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
      :style="!showDelete ? { 'margin-top': '0' } : {}"
    >
      <li :key="file.url" class="px-2 el-upload-list__item ele-upload-list__item-content" v-for="file of _fileList">
        <el-link :underline="false" target="_blank" @click="downloadFile(file.url, file.name)">
          <span class="el-icon-document"> {{ file.name }} </span>
        </el-link>
        <div v-if="showDelete" class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleRemove(file.url)" type="danger">{{ getMessage("删除") }}</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from "vue";
import { ElNotification } from "element-plus";
import type { UploadRequestOptions } from "element-plus";
import { upload } from "@/api/modules/common";
import { getMessage } from "@/utils";
import { downloadFile } from "@/utils/download";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// 定义上传组件的 ref
const fileUpload = ref(null);

interface UploadFileProps {
  fileUrl: { name?: string; url?: string }[];
  baseUrl?: string;
  btnName?: string;
  isShowTip?: boolean;
  showFileList?: boolean;
  api?: (params: any) => Promise<any>;
  drag?: boolean;
  disabled?: boolean;
  limit?: number;
  fileSize?: number;
  fileType?: any[];
  multiple?: boolean;
  showDelete?: boolean;
}

const props = withDefaults(defineProps<UploadFileProps>(), {
  fileUrl: [] as any,
  drag: true,
  btnName: getMessage("选取文件"),
  baseUrl: import.meta.env.VITE_PREVIEW_BASE_URL,
  disabled: false,
  isShowTip: true,
  showFileList: true,
  limit: 5,
  fileSize: 20,
  multiple: true,
  fileType: () => ["doc", "docx", "xls", "ppt", "pptx", "txt", "pdf"],
  showDelete: true
});
interface IEmits {
  (e: "update:fileUrl", payload: { name: string; url: string }[]): void;
  (e: "uploadSuccess", payload: { name: string; url: string }): void;
  (e: "fileListEmpty"): void;
}

const emit = defineEmits<IEmits>();

const _fileList = ref<{ name: string; url: string }[]>(props.fileUrl);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

// 上传文件处理逻辑
const handleHttpUpload = async (options: UploadRequestOptions) => {
  let formData = new FormData();
  formData.append("file", options.file, options.file.name);
  try {
    const api = props.api ?? upload;
    const { code, newFileName, url } = await api(formData);
    if (props.multiple) {
      _fileList.value.push({ name: newFileName, url });
    } else {
      _fileList.value = [{ name: newFileName, url }];
    }
    emit("update:fileUrl", _fileList.value); // 通知父组件文件列表更新
    emit("uploadSuccess", { name: newFileName, url });
    ElNotification({
      title: getMessage("温馨提示"),
      message: getMessage("上传成功！"),
      type: "success"
    });
  } catch (error) {
    options.onError(error as any);
  }
};

// 文件超出限制处理
const handleExceed = () => {
  ElNotification({
    title: getMessage("温馨提示"),
    message: `${getMessage("当前最多只能上传 %s 个文件，请移除后上传！")}`.replace("%s", props.limit),
    type: "warning"
  });
};

// 上传错误处理
const uploadError = () => {
  ElNotification({
    title: getMessage("温馨提示"),
    message: getMessage("图片上传失败，请您重新上传！"),
    type: "error"
  });
};

// 删除文件处理逻辑
const handleRemove = (fileUrl: string) => {
  _fileList.value = _fileList.value.filter(item => item.url !== fileUrl);
  emit("update:fileUrl", _fileList.value); // 通知父组件文件列表更新
  nextTick(() => {
    if (fileUpload.value) {
      (fileUpload.value as any).clearFiles(); // 清除上传状态
    }
  });
  if (_fileList.value.length === 0) {
    emit("fileListEmpty");
  }
};
</script>

<style scoped lang="scss">
.upload-file-list .el-upload-list__item {
  position: relative;
  margin-bottom: 10px;
  line-height: 2;
  border: 1px solid #e4e7ed;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-left: 10px;
}
</style>
