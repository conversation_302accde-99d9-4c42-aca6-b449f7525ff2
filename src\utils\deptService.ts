import { ref } from "vue";
import { getDeptList as SMTDeptList } from "@/api/modules/ipqc-smt/dept_smt";
import { DeptSmt } from "@/typings/ipqc-smt/dept_smt";

export const smtDeptList = ref<DeptSmt.Item[]>([]);
export const getDeptList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await SMTDeptList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      smtDeptList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
