import { ref } from "vue";
import { getObaDipWorkCenterList } from "@/api/modules/oba-dip/oba_dip_work_center";
import { ObaDipWorkCenter } from "@/typings/oba-dip/oba_dip_work_center";

export const workerList = ref<ObaDipWorkCenter.Item[]>([]);
export const getWorkCenterList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getObaDipWorkCenterList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      workerList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
