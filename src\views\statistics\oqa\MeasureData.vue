<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
      @search="tableSearch"
    >
      <template #appendTableTop>
        <div><MeasureDataChart ref="measureDataChartRef" :query-params="queryParams" /></div>
      </template>
      <!--       表格 header 按钮 -->
      <template #tableHeader>
        <!--        <el-button type="primary" @click="handleImport">Import</el-button>-->
        <el-button v-auth="'statistics:opa:export'" type="primary" @click="handleExport">Export</el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button v-auth="'statistics:opa:edit'" type="primary" link @click="handleModalOperation('Edit', row)">Edit</el-button>
        <el-button v-auth="'statistics:opa:del'" type="danger" link @click="handleRemove(row)">Del</el-button>
      </template>
    </ProTable>
    <ImportExcel ref="ImportExcelRef" />

    <handleModal ref="handleModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { ref, reactive, onMounted, nextTick } from "vue";

import { Role } from "@/typings/role";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import MeasureDataChart from "@/views/statistics/oqa/components/MeasureDataChart.vue";

import {
  ipqaMeasureExport,
  oqaMeasureDel,
  oqaMeasureEdit,
  oqaMeasureExport,
  oqaMeasureImport,
  oqaMeasureList
} from "@/api/modules/productionData";
import ImportExcel from "@/components/ImportExcel/index.vue";
import { DimensionsQuery, DimensionsItem } from "@/typings/productionData";
import DateRange from "@/views/components/DateRange.vue";
import { useDict } from "@/hooks/useDict";
import { isEmpty } from "@/utils/is";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
import { formatParams } from "@/utils/util";
import { downloadFileByName } from "@/utils/download";
import { oqaVmiDataLarExport } from "@/api/modules/productionDataAnalysis";
import { ExceptionManageItem } from "@/typings/exception-manage";
import { useHandleData } from "@/hooks/useHandleData";

import handleModal from "@/views/statistics/oqa/components/handleModal.vue";
import { getOperationColWidth, visibleOperationCol } from "@/utils";
import { useAuthStore } from "@/stores/modules/auth";
import { useI18n } from "vue-i18n";
const handleModalRef = ref<InstanceType<typeof handleModal>>();
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>();
const proTable = ref<ProTableInstance>();
const { product_names, production_model, production_parameter } = useDict(
  "product_names",
  "production_model",
  "production_parameter"
);
const { check, currentRow } = useCheckSelectId();
const measureDataChartRef = ref<InstanceType<typeof MeasureDataChart>>();
const { t } = useI18n();
let initParam = reactive({
  pageSize: 10,
  pageNum: 1,
  condition: {}
});
const supplierList = ref<Supplier.Item[]>([]);
const auth = useAuthStore();
const pageButtons = ["statistics:opa:edit", "statistics:opa:del"];
const columns = reactive<ColumnProps<DimensionsItem>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  { type: "index", label: "NO", width: 70 },
  {
    prop: "date",
    label: "Date",
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "supplier",
    label: "Supplier",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "product",
    label: "Product",
    enum: product_names,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.product} placeholder={t("请选择")} clearable>
            {product_names.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "model",
    label: "Model",
    enum: production_model,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.model} placeholder={t("请选择")} clearable>
            {production_model.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "doNo",
    label: "DO",
    search: { el: "input", render: () => <el-input v-model={initParam.doNo} clearable placeholder="请输入" /> }
  },

  // {
  //   prop: "lotNo",
  //   label: "Lot No",
  //   search: { el: "input", render: () => <el-input v-model={initParam.lotNo} clearable placeholder="请输入" /> }
  // },

  {
    prop: "weekCode",
    label: "WeekCode",
    search: { el: "input", render: () => <el-input v-model={initParam.weekCode} clearable placeholder="请输入" /> }
  },

  {
    prop: "parameter",
    label: "Parameter",
    enum: production_parameter,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.parameter} placeholder="请选择" clearable>
            {production_parameter.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "sn", label: "SN" },
  { prop: "measureData", label: "Measure Data" },
  { prop: "usl", label: "USL" },
  { prop: "lsl", label: "LSL" },
  { prop: "createBy", label: "Creator" },
  { prop: "createTime", label: "Create Time" },
  ...(visibleOperationCol(auth.authButtonList, pageButtons)
    ? [
        {
          prop: "operation",
          label: "Operation",
          width: getOperationColWidth(auth.authButtonList, pageButtons),
          fixed: "right"
        }
      ]
    : [])
]);
let queryParams = reactive<DimensionsQuery.IQueryParams>({} as DimensionsQuery.IQueryParams);

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  // const { pageNum, pageSize, ...condition } = params;
  //
  // if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
  //   condition.startDate = filterDate.value[0];
  //   condition.endDate = filterDate.value[1];
  // }
  //
  // queryParams = reactive(condition);
  const result = formatParams(initParam, params, filterDate.value);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return oqaMeasureList(initParam);
};
const tableSearch = () => {
  measureDataChartRef.value?.getData(initParam);
};
/**
 * 批量导入
 */
const handleImport = () => {
  const params = {
    title: "OQA Dimensions",
    importApi: oqaMeasureImport,
    getTableList: proTable.value?.getTableList
  };
  ImportExcelRef.value?.acceptParams(params);
};
/**
 * 导出
 */

const handleExport = async () => {
  await downloadFileByName(oqaMeasureExport, initParam.condition);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  await nextTick(proTable.value?.getTableList);
  measureDataChartRef.value?.getData(initParam);
};
const handleModalOperation = (title: string, row?: ExceptionManageItem) => {
  const form = isEmpty(row?.id) ? {} : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    api: oqaMeasureEdit,
    getTableList: proTable.value?.getTableList
  };
  handleModalRef.value?.acceptParams(params);
};

/**
 * 删除
 * @param row
 */
const handleRemove = async (row: DimensionsItem) => {
  await useHandleData(oqaMeasureDel, { ids: [row.id] }, `Delete selected data`);
  proTable.value?.getTableList();
};
onMounted(() => {
  getSupplierData();
  nextTick(() => {
    measureDataChartRef.value?.getData(initParam);
  });
});
</script>
