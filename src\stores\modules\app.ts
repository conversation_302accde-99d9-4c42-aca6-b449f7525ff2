import { defineStore } from "pinia";
import { AppState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { App } from "@/typings/app";
import { getAppDetail } from "@/api/modules/app";

const useAppStore = defineStore({
  id: "app",
  state: (): AppState => ({
    appInfo: {} as App.Item
  }),
  getters: {},
  actions: {
    setAppInfo(appInfo: App.Item) {
      this.appInfo = appInfo;
    },
    async getAppInfo(code: string) {
      // const { data } = await getAppDetail({ code });
      // this.setAppInfo(data);
    }
  },
  persist: piniaPersistConfig("app")
});

export default useAppStore;
