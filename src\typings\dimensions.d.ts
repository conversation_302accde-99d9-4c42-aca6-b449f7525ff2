/**
 * core_ipqa，IPQA记录表
 */
export interface DimensionsQuery {
  /**
   * 创建时间
   */
  create_time: Date;
  /**
   * 创建人ID
   */
  creator_id: number;
  /**
   * 日期（格式：YYYY-MM-DD）
   */
  date?: Date;
  /**
   * ID
   */
  id: number;
  /**
   * LSL
   */
  LSL?: string;
  /**
   * Measure Data
   */
  measure_data?: string;
  /**
   * 型号
   */
  model?: number;
  /**
   * Parameter
   */
  parameter?: string;
  /**
   * 产品
   */
  product?: string;
  /**
   * Remark
   */
  remark?: string;
  /**
   * Result
   */
  result?: string;
  /**
   * SN
   */
  sn?: string;
  /**
   * station
   */
  station?: string;
  /**
   * 供应商
   */
  supplier?: string;
  /**
   * 更新时间
   */
  update_time: Date;
  /**
   * 修改人ID
   */
  updater_id: number;
  /**
   * USL
   */
  USL?: string;
  /**
   * 工单号
   */
  work_order_no?: string;
  [property: string]: any;
}
export interface DimensionsItem {
  /**
   * 附件名称
   */
  attachment_name: string;
  /**
   * 附件URL
   */
  attachment_url: string;
  create_time: string;
  creator_id: number;
  /**
   * 当前节点ID
   */
  current_node_id: number;
  /**
   * 当前节点
   */
  current_node_name: string;
  /**
   * 天数
   */
  day: string;
  /**
   * 异常描述
   */
  exception_description: string;
  /**
   * 异常类型（CIP/8D）
   */
  exception_type: string;
  id: number;
  /**
   * 产品
   */
  product: number;
  /**
   * 报告编号
   */
  report_no: string;
  /**
   * 进行中
   */
  report_status: number;
  /**
   * 责任人
   */
  responsible_person: number;
  /**
   * 来源（1=IQA，2=QCAN，3=其他）
   */
  source: number;
  /**
   * 来源编号
   */
  source_no: string;
  /**
   * 提交时间
   */
  submit_time: string;
  /**
   * 提交人
   */
  submitter_by: string;
  /**
   * 提交人工号
   */
  submitter_no: string;
  /**
   * 供应商
   */
  supplier: number;
  /**
   * 异常标题
   */
  title: string;
  update_time: string;
  updater_id: number;
  [property: string]: any;
}
