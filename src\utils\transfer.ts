import { ElMessage } from "element-plus";
import { getMessage } from "@/utils";
export const openTransferModal = (
  currentRow: any,
  TransferModalRef: any,
  proTable: any,
  check: any,
  reassignAuditor: any,
  PendingApproval: string
) => {
  check();
  if (currentRow.value.auditStatus !== PendingApproval) {
    ElMessage.error(getMessage("当前状态不可转办"));
    return;
  }
  const { id, auditorNo, auditorName, auditorEmail } = currentRow.value;
  TransferModalRef.value?.acceptParams({
    form: { id, auditorNo, auditorName, auditorEmail },
    api: reassignAuditor,
    getTableList: proTable.value?.getTableList
  });
};
