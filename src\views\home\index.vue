<template>
  <div class="gap-2 overflow-auto fit flex flex-col pb-2" style="overflow-y: auto !important">
    <UserInfo />
    <!--    <QuickEntry :admin="admin" />-->
    <!--    <TodoList :admin="admin" />-->
  </div>
</template>

<script setup lang="ts" name="home">
import QuickEntry from "./components/QuickEntry.vue";
import UserInfo from "./components/UserInfo.vue";
import TodoList from "./components/TodoList.vue";
import { ref, onMounted, computed } from "vue";
import { getAppList } from "@/api/modules/app";
import { App } from "@/typings/app";

const appList = ref<App.Item[]>([]);

const admin = computed(() => appList.value.find(({ code }) => code === "admin") || ({} as App.Item));

// const _getAppList = async () => {
//   const {
//     data: { list }
//   } = await getAppList();
//   appList.value = list;
// };

// onMounted(() => {
//   _getAppList();
// });
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
