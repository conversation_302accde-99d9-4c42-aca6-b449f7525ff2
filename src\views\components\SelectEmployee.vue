<template>
  <el-dialog v-model="dialogVisible" width="80%" :destroy-on-close="true" :title="$t('人员搜索')">
    <div class="table-box">
      <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam">
        <template #tableHeader="{}">
          <el-button type="primary" @click="handleConfirm()">{{ $t("选择") }}</el-button>
        </template>
      </ProTable>
      <QualityPersonModal ref="QualityPersonModalRef" />
      <ImportExcel ref="importModalRef" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts" name="SelectEmployee">
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { Staff } from "@/typings/staff";
import { getStaffFilterList } from "@/api/modules/staff";
import { useI18n } from "vue-i18n";

const proTable = ref<ProTableInstance>();
const { t } = useI18n();
const initParam = reactive({});

// 表格配置项
const columns = reactive<ColumnProps<Staff.Item>[]>([
  { type: "radio", fixed: "left", width: 60 },
  { type: "index", label: "序号", width: 80 },
  { prop: "jobNum", label: "工号", width: 120, search: { el: "input" } },
  { prop: "name", label: "姓名", width: 120, search: { el: "input" } },
  { prop: "deptName", label: "部门" },
  { prop: "postName", label: "岗位" },
  { prop: "email", label: "邮箱" },
  { prop: "leaderName", label: "直接领导" },
  { prop: "leaderEmail", label: "直接领导邮箱" }
]);

const getTableList = async (params: any) => {
  const res = await getStaffFilterList(params);
  employeeList.value = res?.data?.list;
  console.log("employeeList.value", employeeList.value);
  return res;
};

const dialogVisible = ref(false);
const employeeList = ref<Staff.Item[]>([]);
const emit = defineEmits(["select"]);

const handleConfirm = () => {
  const currId = proTable.value?.radio;
  if (currId > 0) {
    const row = employeeList.value.find(item => item.id === currId) as Staff.Item;
    if (!row.email) {
      ElMessage.error(t("选择员工没有邮箱"));
      return;
    }
    if (!row.leaderEmail) {
      ElMessage.error(t("选择员工直接领导没有邮箱"));
      return;
    }
    emit("select", row);
    dialogVisible.value = false;
  } else {
    ElMessage.error(t("请选择一位员工"));
  }
};
const acceptParams = async () => {
  dialogVisible.value = true;
};

defineExpose({
  acceptParams
});
</script>
