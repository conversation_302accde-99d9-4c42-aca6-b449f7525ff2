<template>
  <el-card class="left-params">
    <p>{{ $t("规格下限") }}: {{ histogramCalculate?.LSL }}</p>
    <p>{{ $t("规格目标") }}: {{ histogramCalculate?.Target }}</p>
    <p>{{ $t("规格上限") }}: {{ histogramCalculate?.USL }}</p>
    <p>{{ $t("样本均值") }}: {{ histogramCalculate?.Mean }}</p>
    <p>{{ $t("样本数量") }}: {{ histogramCalculate?.n }}</p>
    <p>{{ $t("标准差(总体)") }}: {{ histogramCalculate?.SigmaWithin }}</p>
    <p>{{ $t("标准差(组内)") }}: {{ histogramCalculate?.SigmaWithin }}</p>
    <p>{{ $t("p值") }}: {{ histogramCalculate?.p }}</p>
    <p>{{ $t("变异系数") }}: {{ histogramCalculate?.CV }}</p>
  </el-card>
  <el-card class="right-params">
    <div style="position: relative">
      <div class="expand" @click="expand">
        <el-icon><View v-if="!show" /><Hide v-if="show" /></el-icon>
      </div>
      <p class="title">{{ $t("整体能力") }}</p>
      <template v-if="show">
        <p>Pp: {{ histogramCalculate?.Pp }}</p>
        <p>PPL: {{ histogramCalculate?.PPL }}</p>
        <p>PPU: {{ histogramCalculate?.PPU }}</p>
        <p>Ppk: {{ histogramCalculate?.Ppk }}</p>
        <p>Cpm: {{ histogramCalculate?.Cpm }}</p>
        <p>Ca: {{ histogramCalculate?.Ca }}</p>
        <p class="title" style="margin-top: 4px">{{ $t("潜在(组内)能力") }}</p>
        <p>Cp: {{ histogramCalculate?.Cp }}</p>
        <p>CPL: {{ histogramCalculate?.CPL }}</p>
        <p>CPU: {{ histogramCalculate?.CPU }}</p>
        <p>Cpk: {{ histogramCalculate?.Cpk }}</p>
        <div class="lengend">
          <div class="item">{{ $t("整体") }}</div>
          <div class="item">{{ $t("组内") }}</div>
        </div>
      </template>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";

const props: any = defineProps({
  // eslint-disable-next-line vue/require-prop-types
  histogramCalculate: {
    USL: 0,
    LSL: 0,
    Target: 0,
    Mean: 0,
    n: 0,
    SigmaWithin: 0,
    SigmaTotal: 0,
    CV: 0,
    AD: 0,
    p: 0,
    normalDistribution: true,
    Ca: 0,
    Cp: 0,
    Cpk: 0,
    Pp: 0,
    Ppk: 0,
    Cpm: 0,
    PPL: 0,
    PPU: 0,
    CPL: 0,
    CPU: 0
  }
});
const show = ref(true);
const expand = () => {
  show.value = !show.value;
};
const histogramCalculate = computed(() => {
  return props.histogramCalculate;
});
</script>
<style scoped lang="scss">
.left-params {
  position: absolute;
  top: -30px;
  left: 0;
  line-height: 16px;
  font-size: 12px;
  z-index: 111;
  ::v-deep(.el-card__body) {
    padding: 10px;
  }
}
.right-params {
  position: absolute;
  top: -30px;
  right: 0;
  line-height: 16px;
  font-size: 12px;
  z-index: 111;
  min-width: 125px;
  .title {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 13px;
  }
  ::v-deep(.el-card__body) {
    padding: 10px;
  }
}

.lengend {
  margin-top: 5px;
  .item {
    display: inline-block;
    //font-weight: bold;
    font-size: 13px;
    position: relative;
    margin-right: 5px;
    &:before {
      content: "";
      display: inline-block;
      width: 16px;
      height: 0;
      border-top: 3px dashed red;
      vertical-align: middle;
      margin-right: 4px;
    }
    &:nth-child(2) {
      &:before {
        border-color: green;
      }
    }
  }
}
.expand {
  text-align: right;
  font-size: 24px;
  font-weight: bold;
  line-height: 16px;
  position: absolute;
  right: -4px;
  top: -4px;
  cursor: pointer;
  span {
    vertical-align: middle;
  }
}
</style>
