<script setup lang="tsx" name="Table">
import { ColumnProps } from "@/components/ProTable/interface";
import { computed } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const columns = computed<ColumnProps<any>[]>(() => {
  return [
    {
      prop: "statsDate",
      label: "",
      width: "120"
    },
    ...props.fields.map(
      v =>
        ({
          prop: v,
          label: v,
          width: v === "-" ? firstColumnMax.value : 120,
          showOverflowTooltip: false,
          fixed: v === "-" ? "left" : false
        }) as ColumnProps<any>
    )
  ];
});

const props = defineProps<{
  tableData: any;
  fields: any;
}>();

const firstColumnMax = computed(() => {
  const length = props.tableData?.reduce((max: any, current: any) => {
    return Math.max(max, current["-"].length);
  }, 0);
  return Math.max(length * 15, 120);
});
</script>
<template>
  <div id="chart-table" class="table-box is-fill-cell" style="overflow: scroll">
    <ProTable ref="proTable" :data="props.tableData" :columns="columns" :tool-button="[]" :pagination="false" scrollbar-always-on>
    </ProTable>
  </div>
</template>
<style>
.table-box .el-table .el-table__body-wrapper,
.table-main .el-table .el-table__body-wrapper {
  min-height: auto;
}
#chart-table {
  overflow: scroll;
}
#chart-table .card {
  border: 0 !important;
}
</style>
