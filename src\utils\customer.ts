import { ref } from "vue";
import { getCustomerInfoList } from "@/api/modules/customer-satisfaction/customer_info";
import { CustomerInfo } from "@/typings/customer-satisfaction/customer_info";

export const customerList = ref<CustomerInfo.Item[]>([]);
export const getCustomerList = async () => {
  try {
    const {
      success,
      data: { list }
    } = await getCustomerInfoList({ condition: {}, pageNum: 1, pageSize: 10000 });
    if (success) {
      customerList.value = list || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
