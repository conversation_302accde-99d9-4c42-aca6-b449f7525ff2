<script setup lang="tsx" name="Table">
import { ColumnProps } from "@/components/ProTable/interface";
import { computed } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const columns = computed<ColumnProps<any>[]>(() => {
  return [
    ...props.fields.map(
      v =>
        ({
          prop: v,
          label: v,
          width: 120,
          showOverflowTooltip: false,
          fixed: v === "-" ? "left" : false,
          render: ({ row }) => {
            return row.value;
          }
        }) as ColumnProps<any>
    )
  ];
});

const props = defineProps<{
  tableData: any;
  fields: any;
}>();
</script>

<template>
  <div id="chart-table" class="table-box is-fill-cell" style="overflow: scroll">
    <ProTable ref="proTable" :data="props.tableData" :columns="columns" :tool-button="[]" :pagination="false" scrollbar-always-on>
    </ProTable>
  </div>
</template>
<style>
#chart-table {
  overflow: scroll;
}
#chart-table .card {
  border: 0 !important;
}
</style>
