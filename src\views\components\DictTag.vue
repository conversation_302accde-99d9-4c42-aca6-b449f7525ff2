<template>
  <el-tag v-if="label" class="dict-tag" :type="tagType">
    {{ label }}
  </el-tag>
</template>

<script setup>
import { computed } from "vue";
import { ElTag } from "element-plus";

const props = defineProps({
  dict: {
    type: Array,
    required: true
  },
  label: {
    type: String,
    required: true
  }
});

const tagType = computed(() => {
  return props.dict.find(item => item.label === props.label)?.tagType || "";
});
</script>

<style scoped>
.dict-tag {
  margin-right: 20px;
}
</style>
