import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { DimensionsQuery } from "@/typings/dimensions";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";
import { ExceptionManageForm, ExceptionManageItem } from "@/typings/exception-manage";

/**
 * 列表
 * @param params
 */
export const getExceptionManage = (params: ReqPage) => {
  return http.post<ExceptionManageItem>(`${API_PREFIX}/exceptionReports/list`, params);
};
/**
 * 删除
 * @param params
 */
export const exceptionManageDelete = (params: any) => {
  return http.post(`${API_PREFIX}/exceptionReports/del`, params);
};

/**
 * 删除
 * @param params
 */
export const exceptionManageExport = (params: any) => {
  return http.post(`${API_PREFIX}/exceptionReports/export`, params);
};

/**
 * 新增
 * @param params
 */
export const exceptionManageAdd = (params: any) => {
  return http.post<ExceptionManageForm>(`${API_PREFIX}/exceptionReports/submit`, params);
};
/**
 * 编辑
 * @param params
 */
export const exceptionManageSave = (params: any) => {
  return http.post<ExceptionManageForm>(`${API_PREFIX}/exceptionReports/save`, params);
};

/**
 * 获取单条异常报告详情
 * @param params
 */
export const exceptionManageInfo = (params: any) => {
  return http.get<ExceptionManageForm>(`${API_PREFIX}/exceptionReports/get/` + params.id);
};

/**
 *
 * @param params
 */
export const exceptionReportsSupplierList = (params: any) => {
  return http.post<ExceptionManageForm>(`${API_PREFIX}/exceptionReports/supplierList`, params);
};

/**
 *
 * @param params
 */
export const exceptionReportsMyList = (params: any) => {
  return http.post<ExceptionManageForm>(`${API_PREFIX}/exceptionReports/myList`, params);
};
