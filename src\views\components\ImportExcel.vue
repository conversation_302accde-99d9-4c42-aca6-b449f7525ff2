<template>
  <el-dialog id="import-het" v-model="dialogVisible" :title="parameter.title" :destroy-on-close="true" width="780px" draggable>
    <el-form
      class="drawer-multiColumn-form"
      label-suffix=" :"
      :rules="rules"
      :validate-on-rule-change="false"
      label-width="120"
      ref="formRef"
      :model="form"
      :show-message="isZh"
    >
      <el-form-item :label="$t('模板下载 ')">
        <el-button type="primary" :icon="Download" @click="downloadTemp"> {{ $t("下载模板") }} </el-button>
        &nbsp;&nbsp;&nbsp;&nbsp;
        {{ $t("下载模板后，请勿随意修改模板结构") }}
      </el-form-item>
      <el-form-item :label="$t('工厂')" prop="plant">
        <el-radio-group v-model="form.plant">
          <el-radio :value="item.value" v-for="item in factory" :key="item.value" border>{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="t('上传文件')" prop="file">
        <el-upload
          v-model:file-list="form.file"
          ref="uploadRef"
          action="#"
          class="upload"
          :drag="true"
          :limit="excelLimit"
          :multiple="false"
          :show-file-list="true"
          :http-request="uploadExcel"
          :before-upload="beforeExcelUpload"
          :on-exceed="handleExceed"
          :on-success="excelUploadSuccess"
          :on-error="excelUploadError"
          :auto-upload="false"
          :accept="parameter.fileType!.join(',')"
        >
          <slot name="empty">
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              {{ $t("将文件拖到此处，或点击上传") }}
            </div>
          </slot>
          <template #tip>
            <slot name="tip">
              <div class="el-upload__tip">{{ $t("请上传 .xls , .xlsx 标准格式文件，文件最大为") }} {{ parameter.fileSize }}M</div>
            </slot>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleUpload">{{ $t("导入") }}</el-button>
        <el-button @click="closeDialog">{{ $t("关闭") }}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts" name="ImportExcel">
import { ref } from "vue";
import { Download } from "@element-plus/icons-vue";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { ElNotification, UploadRequestOptions, UploadRawFile, FormInstance, ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { download } from "@/api/modules/common";
import { useLanguageCode } from "@/hooks/useLanguageCode";
const { isZh } = useLanguageCode();
export interface ExcelParameterProps {
  title: string; // 标题
  fileSize?: number; // 上传文件的大小
  fileType?: File.ExcelMimeType[]; // 上传文件的类型
  tempApi?: (params: any) => Promise<any>; // 下载模板的Api
  importApi?: (params: any) => Promise<any>; // 批量导入的Api
  getTableList?: () => void; // 获取表格数据的Api
}

const { factory } = useAdminDict("factory");
const formRef = ref<FormInstance>();
const form = ref({
  plant: "",
  statsTime: "",
  file: []
});
// 是否覆盖数据
const isCover = ref(false);

// 最大文件上传数
const excelLimit = ref(1);
// dialog状态
const dialogVisible = ref(false);
const { t } = useI18n();
// 父组件传过来的参数
const parameter = ref<ExcelParameterProps>({
  title: "",
  fileSize: 5,
  fileType: ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
});

// 上传组件的引用
const uploadRef = ref<InstanceType<(typeof import("element-plus"))["ElUpload"]> | null>(null);

// 接收父组件参数
const acceptParams = (params: ExcelParameterProps) => {
  parameter.value = { ...parameter.value, ...params };
  dialogVisible.value = true;
};

// Excel 导入模板下载
const downloadTemp = async () => {
  if (!parameter.value.tempApi) return;
  download(parameter.value.tempApi, true);
};

// 文件上传
const uploadExcel = async (param: UploadRequestOptions) => {
  let excelFormData = new FormData();
  const plantName = factory.value.find(item => item.value === form.value.plant)?.label ?? "";
  excelFormData.append("file", param.file);
  excelFormData.append("plant", plantName);
  try {
    await parameter.value.importApi!(excelFormData);
    ElNotification({
      title: t("温馨提示"),
      message: `${t("批量添加")}${parameter.value.title}${t("成功！")}`,
      type: "success"
    });
    parameter.value.getTableList && parameter.value.getTableList();
    dialogVisible.value = false;
  } catch (error) {
    ElNotification({
      title: t("温馨提示"),
      message: `${t("批量添加")}${parameter.value.title}${t("失败，请您重新上传！")}`,
      type: "error"
    });
  }
};

/**
 * @description 文件上传之前判断
 * @param file 上传的文件
 * */
const beforeExcelUpload = (file: UploadRawFile) => {
  const isExcel = parameter.value.fileType!.includes(file.type as File.ExcelMimeType);
  const fileSize = file.size / 1024 / 1024 < parameter.value.fileSize!;
  if (!isExcel)
    ElNotification({
      title: t("温馨提示"),
      message: t("上传文件只能是 xls / xlsx 格式！"),
      type: "warning"
    });
  if (!fileSize)
    setTimeout(() => {
      ElNotification({
        title: t("温馨提示"),
        message: `${t("上传文件大小不能超过")} ${parameter.value.fileSize}MB！`,
        type: "warning"
      });
    }, 0);
  return isExcel && fileSize;
};

// 文件数超出提示
const handleExceed = () => {
  ElNotification({
    title: t("温馨提示"),
    message: t("最多只能上传一个文件！"),
    type: "warning"
  });
};

// 上传错误提示
const excelUploadError = () => {
  ElNotification({
    title: t("温馨提示"),
    message: `${t("批量添加")}${parameter.value.title}${t("失败，请您重新上传！")}`,
    type: "error"
  });
};

// 上传成功提示
const excelUploadSuccess = () => {
  ElNotification({
    title: t("温馨提示"),
    message: `${t("批量添加")}${parameter.value.title}${t("成功！")}`,
    type: "success"
  });
};

const rules = {
  plant: [{ required: true, message: "请选择工厂", trigger: "blur" }],
  file: [{ required: true, message: "请选择文件", trigger: "blur" }]
};

// 触发上传
const handleUpload = async () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      uploadRef.value?.submit();
    } catch (error) {
      console.log(error);
    }
  });
};

const closeDialog = () => {
  dialogVisible.value = false;
};

defineExpose({
  acceptParams
});
</script>

<style>
#import-het .el-radio-group {
  display: block;
}
#import-het .el-radio {
  margin-right: 8px;
}
</style>
