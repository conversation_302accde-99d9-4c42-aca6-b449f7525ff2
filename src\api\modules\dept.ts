import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Dept } from "@/typings/dept";
import { API_PREFIX } from "@/api/config/servicePort";

export const getDeptList = (params?: ReqPage) => {
  return http.get<Dept.Item[]>(`${API_PREFIX}/system/dept/list`, params);
};

export const getDeptAll = () => {
  return http.get<Dept.Item[]>(`${API_PREFIX}/system/user/deptTree`, {}, { loading: false });
};

export const getDeptDetail = (deptId: number) => {
  return http.get<Dept.Item>(`${API_PREFIX}/${deptId}`);
};

export const createDept = (data: Dept.Item) => {
  return http.post(`${API_PREFIX}/system/dept`, data);
};

export const editDept = (data: Dept.Item) => {
  return http.put(`${API_PREFIX}/system/dept`, data);
};

export const deleteDept = (id: string) => {
  return http.delete(`${API_PREFIX}/system/dept/${id}`);
};
